const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkSchemaDetails() {
  console.log('🔍 COMPREHENSIVE BOOKSCRIBE DATABASE SCHEMA ANALYSIS');
  console.log('===================================================\n');
  
  // Check each existing table's column structure
  const existingTables = [
    'projects', 'chapters', 'characters', 'story_arcs', 'story_bible',
    'selection_profiles', 'reference_materials', 'editing_sessions', 'agent_logs'
  ];
  
  for (const tableName of existingTables) {
    console.log(`📋 TABLE: ${tableName.toUpperCase()}`);
    console.log('='.repeat(50));
    
    try {
      // Use raw SQL to get column information
      const query = `
        SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default,
          character_maximum_length
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = '${tableName}'
        ORDER BY ordinal_position;
      `;
      
      const { data, error } = await supabase.rpc('exec_sql', { sql: query });
      
      if (error) {
        console.log(`❌ Error querying ${tableName}: ${error.message}`);
      } else if (data && data.length > 0) {
        console.log(`Columns (${data.length}):`);
        data.forEach((col, i) => {
          const nullable = col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL';
          const defaultVal = col.column_default ? ` DEFAULT ${col.column_default}` : '';
          const maxLen = col.character_maximum_length ? `(${col.character_maximum_length})` : '';
          console.log(`  ${i + 1}. ${col.column_name} - ${col.data_type}${maxLen} ${nullable}${defaultVal}`);
        });
      } else {
        console.log('No columns found');
      }
      
    } catch (err) {
      console.log(`❌ Exception: ${err.message}`);
    }
    
    console.log();
  }
  
  // Now check what's missing from the expected schema
  console.log('🔍 SCHEMA GAP ANALYSIS');
  console.log('======================\n');
  
  // Check projects table specifically
  try {
    const query = `
      SELECT column_name
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'projects'
      ORDER BY ordinal_position;
    `;
    
    const { data, error } = await supabase.rpc('exec_sql', { sql: query });
    
    if (data && data.length > 0) {
      const existingColumns = data.map(row => row.column_name);
      
      const expectedProjectColumns = [
        // Core fields
        'id', 'user_id', 'title', 'description', 'status', 'created_at', 'updated_at',
        // Genre & Style
        'primary_genre', 'subgenre', 'custom_genre', 'narrative_voice', 'tense', 
        'tone_options', 'writing_style', 'custom_style_description',
        // Structure & Pacing
        'structure_type', 'pacing_preference', 'chapter_structure', 'timeline_complexity', 'custom_structure_notes',
        // Character & World
        'protagonist_types', 'antagonist_types', 'character_complexity', 'character_arc_types', 
        'custom_character_concepts', 'time_period', 'geographic_setting', 'world_type', 
        'magic_tech_level', 'custom_setting_description',
        // Themes & Content
        'major_themes', 'philosophical_themes', 'social_themes', 'custom_themes', 
        'target_audience', 'content_rating', 'content_warnings', 'cultural_sensitivity_notes',
        // Series & Scope
        'project_scope', 'series_type', 'interconnection_level', 'custom_scope_description',
        // Technical
        'target_word_count', 'current_word_count', 'target_chapters', 'chapter_count_type', 
        'pov_character_count', 'pov_character_type',
        // Research
        'research_needs', 'fact_checking_level', 'custom_research_notes',
        // Enhanced
        'initial_concept'
      ];
      
      const missingColumns = expectedProjectColumns.filter(col => !existingColumns.includes(col));
      
      console.log('📊 PROJECTS TABLE COLUMN ANALYSIS:');
      console.log(`✅ Existing columns: ${existingColumns.length}`);
      console.log(`❌ Missing columns: ${missingColumns.length}`);
      
      if (missingColumns.length > 0) {
        console.log('\nMissing project columns:');
        missingColumns.forEach(col => console.log(`  - ${col}`));
      }
      
      console.log('\nExisting project columns:');
      existingColumns.forEach(col => console.log(`  ✓ ${col}`));
    }
  } catch (err) {
    console.log('❌ Cannot analyze projects table columns');
  }
  
  console.log('\n='.repeat(60));
  console.log('CRITICAL MISSING TABLES SUMMARY:');
  console.log('='.repeat(60));
  
  const criticalMissingTables = [
    'profiles', 'user_subscriptions', 'usage_tracking', 'usage_events',
    'content_embeddings', 'writing_sessions', 'notifications', 'ai_suggestions'
  ];
  
  criticalMissingTables.forEach(table => {
    console.log(`❌ ${table} - Required for authentication, billing, analytics, and AI features`);
  });
}

checkSchemaDetails().catch(console.error);