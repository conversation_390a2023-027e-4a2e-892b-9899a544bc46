#!/usr/bin/env tsx

/**
 * Verify that Stripe Sync Engine setup was successful
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

async function verifyStripeSetup() {
  console.log('🔍 Verifying Stripe Sync Engine setup...')
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY
  
  if (!supabaseUrl || !serviceRoleKey) {
    console.error('❌ Missing required environment variables')
    process.exit(1)
  }
  
  const supabase = createClient(supabaseUrl, serviceRoleKey)
  
  try {
    // Check if stripe schema exists
    console.log('📊 Checking for stripe schema...')
    const { data: schemas, error: schemaError } = await supabase
      .rpc('get_schemas')
      .then(result => {
        if (result.error) {
          // Fallback query if RPC doesn't exist
          return supabase
            .from('information_schema.schemata')
            .select('schema_name')
            .eq('schema_name', 'stripe')
        }
        return result
      })
    
    if (schemaError) {
      console.log('⚠️  Could not check schema directly, trying table check...')
    } else if (schemas && schemas.length > 0) {
      console.log('✅ Stripe schema found')
    } else {
      console.log('❌ Stripe schema not found')
    }
    
    // Check for stripe tables
    console.log('📋 Checking for stripe tables...')
    const expectedTables = [
      'customers',
      'subscriptions', 
      'invoices',
      'payment_intents',
      'charges',
      'products',
      'prices'
    ]
    
    const foundTables: string[] = []
    
    for (const table of expectedTables) {
      try {
        const { data, error } = await supabase
          .from(`stripe.${table}`)
          .select('*')
          .limit(1)
        
        if (!error) {
          foundTables.push(table)
          console.log(`  ✅ stripe.${table}`)
        } else {
          console.log(`  ❌ stripe.${table} - ${error.message}`)
        }
      } catch (err) {
        console.log(`  ❌ stripe.${table} - Error checking table`)
      }
    }
    
    console.log(`\n📊 Summary:`)
    console.log(`   Found ${foundTables.length}/${expectedTables.length} expected tables`)
    
    if (foundTables.length > 0) {
      console.log('✅ Stripe Sync Engine appears to be set up correctly!')
      console.log('\n🔗 Next steps:')
      console.log('   1. Configure Stripe webhooks')
      console.log('   2. Test webhook processing')
      console.log('   3. Monitor the admin dashboard')
    } else {
      console.log('❌ Stripe Sync Engine setup may have failed')
      console.log('\n💡 Try running the setup again or check your database permissions')
    }
    
  } catch (error) {
    console.error('❌ Verification failed:', error)
    process.exit(1)
  }
}

verifyStripeSetup().catch(console.error)
