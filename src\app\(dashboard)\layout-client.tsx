'use client'

import { Navbar } from '@/components/layout/navbar'
import { useBreadcrumbs } from '@/hooks/use-breadcrumbs'

export function DashboardLayoutClient({ children }: { children: React.ReactNode }) {
  const breadcrumbs = useBreadcrumbs()
  
  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground">
      <Navbar breadcrumbs={breadcrumbs} />
      <main className="flex-1">
        {children}
      </main>
    </div>
  )
}