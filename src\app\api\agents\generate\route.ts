import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { AdvancedAgentOrchestrator } from '@/lib/agents/advanced-orchestrator'
import { WritingAgent } from '@/lib/agents/writing-agent'
import type { BookContext } from '@/lib/agents/types'
import { ChapterGenerator } from '@/lib/services/chapter-generator'
import type { ChapterContext } from '@/lib/services/chapter-generator'
import { getClientIP } from '@/lib/rate-limiter'
import { AI_RATE_LIMITS, createAIRateLimitResponse } from '@/lib/rate-limiter-ai'
import { checkUsageBeforeAction, trackUsage } from '@/lib/usage-tracker'
import { z } from 'zod'
import { generateStructureWithTransaction } from '@/lib/db/transactions/structure-generation'

// Validation schemas
const generateRequestSchema = z.object({
  projectId: z.string().uuid(),
  action: z.enum(['generate_structure', 'generate_chapter', 'expand_chapter', 'edit_chapter', 'write_chapter', 'generate_chapter_enhanced']),
  chapterNumber: z.number().int().positive().optional(),
  editInstructions: z.string().optional(),
  regenerateOption: z.enum(['structure', 'characters', 'events']).optional()
})

export async function POST(request: NextRequest) {
  try {
    // Rate limiting check - stricter for expensive AI operations
    const clientIP = getClientIP(request)
    const { limiter, requests } = AI_RATE_LIMITS.generation
    const rateLimitResult = limiter.check(requests, clientIP)
    
    if (!rateLimitResult.success) {
      return createAIRateLimitResponse(rateLimitResult.reset)
    }
    
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check usage limits before proceeding
    const usageCheck = await checkUsageBeforeAction(user.id, 'ai_generation')
    if (!usageCheck.allowed) {
      return NextResponse.json({ 
        error: usageCheck.reason,
        upgradeRequired: true,
        usage: usageCheck.usage,
        limits: usageCheck.limits
      }, { status: 403 })
    }

    const body = await request.json()
    
    // Validate request body
    const validationResult = generateRequestSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json({ 
        error: 'Invalid request data', 
        details: validationResult.error.errors 
      }, { status: 400 })
    }
    
    const { projectId, action } = validationResult.data

    // Get project from database
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    const orchestrator = new AdvancedAgentOrchestrator()

    switch (action) {
      case 'generate_structure': {
        console.log('Generating complete project structure...')
        
        // Map database project to ProjectSettings format
        const projectSettings = {
          projectName: project.title,
          description: project.description || '',
          initialConcept: project.initial_concept || project.description || '',
          primaryGenre: project.primary_genre,
          subgenre: project.subgenre,
          customGenre: project.custom_genre,
          narrativeVoice: project.narrative_voice,
          tense: project.tense,
          tone: project.tone_options || [],
          writingStyle: project.writing_style,
          customStyleDescription: project.custom_style_description,
          structureType: project.structure_type,
          pacingPreference: project.pacing_preference,
          chapterStructure: project.chapter_structure,
          timelineComplexity: project.timeline_complexity,
          customStructureNotes: project.custom_structure_notes,
          protagonistTypes: project.protagonist_types,
          antagonistTypes: project.antagonist_types,
          characterComplexity: project.character_complexity,
          characterArcTypes: project.character_arc_types,
          customCharacterConcepts: project.custom_character_concepts,
          timePeriod: project.time_period,
          geographicSetting: project.geographic_setting,
          worldType: project.world_type,
          magicTechLevel: project.magic_tech_level,
          customSettingDescription: project.custom_setting_description,
          majorThemes: project.major_themes,
          philosophicalThemes: project.philosophical_themes,
          socialThemes: project.social_themes,
          customThemes: project.custom_themes,
          targetAudience: project.target_audience,
          contentRating: project.content_rating,
          contentWarnings: project.content_warnings,
          culturalSensitivityNotes: project.cultural_sensitivity_notes,
          projectScope: project.project_scope,
          seriesType: project.series_type,
          interconnectionLevel: project.interconnection_level,
          customScopeDescription: project.custom_scope_description,
          targetWordCount: project.target_word_count,
          targetChapters: project.target_chapters,
          chapterCountType: project.chapter_count_type,
          povCharacterCount: project.pov_character_count,
          povCharacterType: project.pov_character_type,
          researchNeeds: project.research_needs,
          factCheckingLevel: project.fact_checking_level,
          customResearchNotes: project.custom_research_notes
        }

        const result = await orchestrator.orchestrateProject(
          projectId,
          projectSettings,
          project.description || 'No description provided',
          project.target_word_count || 80000,
          project.target_chapters || 20
        )

        if (!result.success) {
          console.error('Story generation failed:', result.error)
          return NextResponse.json({ error: result.error }, { status: 500 })
        }

        // Store the generated data in the database using transaction
        const context = result.data!
        
        // Transform characters to ensure required fields
        const transformedCharacters = context.characters ? {
          protagonists: context.characters.protagonists?.map(char => ({
            name: char.name,
            role: char.role,
            description: char.description || '',
            backstory: char.backstory || '',
            personality: char.personality as unknown as Record<string, unknown> || {},
            arc: char.arc as unknown as Record<string, unknown> || {},
            relationships: (char.relationships || []) as unknown as Record<string, unknown>[]
          })),
          antagonists: context.characters.antagonists?.map(char => ({
            name: char.name,
            role: char.role,
            description: char.description || '',
            backstory: char.backstory || '',
            personality: char.personality as unknown as Record<string, unknown> || {},
            arc: char.arc as unknown as Record<string, unknown> || {},
            relationships: (char.relationships || []) as unknown as Record<string, unknown>[]
          })),
          supporting: context.characters.supporting?.map(char => ({
            name: char.name,
            role: char.role,
            description: char.description || '',
            backstory: char.backstory || '',
            personality: char.personality as unknown as Record<string, unknown> || {},
            arc: char.arc as unknown as Record<string, unknown> || {},
            relationships: (char.relationships || []) as unknown as Record<string, unknown>[]
          }))
        } : undefined
        
        // Transform story bible to ensure required fields
        const transformedStoryBible = context.storyBible ? {
          world: context.storyBible.world,
          continuity: context.storyBible.continuity,
          timeline: context.storyBible.timeline?.map(event => ({
            id: event.id,
            title: event.title,
            description: event.description,
            timestamp: event.timestamp,
            actNumber: event.actNumber,
            chapterNumber: event.chapterNumber || 0
          }))
        } : undefined
        
        // Transform chapter outlines to ensure required fields
        const transformedChapterOutlines = context.chapterOutlines ? {
          chapters: context.chapterOutlines.chapters?.map(chapter => ({
            number: chapter.number,
            title: chapter.title,
            wordCountTarget: chapter.wordCountTarget,
            ...chapter as unknown as Record<string, unknown>
          }))
        } : undefined

        const transactionResult = await generateStructureWithTransaction({
          projectId,
          storyStructure: context.storyStructure,
          characters: transformedCharacters,
          storyBible: transformedStoryBible,
          chapterOutlines: transformedChapterOutlines
        })
        
        if (!transactionResult.success) {
          console.error('Failed to save generated structure:', transactionResult.error)
          return NextResponse.json({ 
            error: 'Failed to save generated structure. Data has been rolled back.', 
            details: transactionResult.error 
          }, { status: 500 })
        }
        
        // Skip manual storage - handled by transaction
        /* Store story structure
        if (context.storyStructure) {
          await supabase.from('story_arcs').delete().eq('project_id', projectId)
          
          for (const act of context.storyStructure.acts) {
            await supabase.from('story_arcs').insert({
              project_id: projectId,
              act_number: act.number,
              description: act.description,
              key_events: act.keyEvents
            })
          }
        }

        // Store characters
        if (context.characters) {
          const allCharacters = [
            ...(context.characters.protagonists || []),
            ...(context.characters.antagonists || []),
            ...(context.characters.supporting || [])
          ]
          
          if (allCharacters.length > 0) {
            await supabase.from('characters').delete().eq('project_id', projectId)
            
            for (const character of allCharacters) {
              await supabase.from('characters').insert({
                project_id: projectId,
                name: character.name,
                role: character.role,
                description: character.description,
                backstory: character.backstory,
                personality_traits: character.personality,
                character_arc: character.arc,
                relationships: character.relationships
              })
            }
          }
        }

        // Store story bible
        if (context.storyBible) {
          await supabase.from('story_bible').delete().eq('project_id', projectId)
          
          // Store world rules if they exist
          if (context.storyBible.world?.rules) {
            for (const rule of context.storyBible.world.rules) {
              await supabase.from('story_bible').insert({
                project_id: projectId,
                entry_type: 'world_rule',
                entry_key: `rule_${context.storyBible.world.rules.indexOf(rule)}`,
                entry_data: { value: rule }
              })
            }
          }

          // Store plot threads if they exist
          if (context.storyBible.continuity?.plotThreads) {
            for (const thread of context.storyBible.continuity.plotThreads) {
              await supabase.from('story_bible').insert({
                project_id: projectId,
                entry_type: 'plot_thread',
                entry_key: thread.id,
                entry_data: { description: thread.description, status: thread.status }
              })
            }
          }

          // Store timeline events
          if (context.storyBible.timeline) {
            for (const event of context.storyBible.timeline) {
              await supabase.from('story_bible').insert({
                project_id: projectId,
                entry_type: 'timeline_event',
                entry_key: event.id,
                entry_data: { 
                  title: event.title,
                  description: event.description,
                  timestamp: event.timestamp,
                  actNumber: event.actNumber,
                  chapterNumber: event.chapterNumber
                },
                chapter_introduced: event.chapterNumber
              })
            }
          }
        }

        // Store chapter outlines
        if (context.chapterOutlines?.chapters) {
          await supabase.from('chapters').delete().eq('project_id', projectId)
          
          for (const outline of context.chapterOutlines.chapters) {
            await supabase.from('chapters').insert({
              project_id: projectId,
              chapter_number: outline.number,
              title: outline.title,
              target_word_count: outline.wordCountTarget,
              outline: JSON.stringify(outline),
              status: 'planned'
            })
          }
        }

        */ // End of manual storage
        
        // Project status update is handled by transaction

        // Track usage
        await trackUsage({
          userId: user.id,
          eventType: 'ai_generation',
          metadata: { action: 'generate_structure', projectId }
        })

        return NextResponse.json({ 
          success: true, 
          data: context,
          message: 'Project structure generated successfully'
        })
      }

      case 'write_chapter': {
        const { chapterNumber } = validationResult.data
        console.log(`Writing chapter ${chapterNumber}...`)

        // Get project context from database
        const { data: storyArcs } = await supabase
          .from('story_arcs')
          .select('*')
          .eq('project_id', projectId)
          .order('act_number')

        const { data: characters } = await supabase
          .from('characters')
          .select('*')
          .eq('project_id', projectId)

        const { data: storyBible } = await supabase
          .from('story_bible')
          .select('*')
          .eq('project_id', projectId)

        const { data: chapters } = await supabase
          .from('chapters')
          .select('*')
          .eq('project_id', projectId)
          .order('chapter_number')

        if (!storyArcs || !characters || !storyBible) {
          return NextResponse.json({ error: 'Project structure not found. Please generate structure first.' }, { status: 400 })
        }

        // Build context from database
        const context: BookContext = {
          projectId,
          settings: {
            projectName: project.title,
            description: project.description || '',
            primaryGenre: project.primary_genre,
            genre: project.primary_genre,
            subgenre: project.subgenre,
            targetAudience: project.target_audience,
            targetWordCount: project.target_word_count,
            targetChapters: project.target_chapters,
            narrativeVoice: project.narrative_voice,
            tense: project.tense,
            tone: project.tone_options || [],
            writingStyle: project.writing_style,
            contentRating: project.content_rating,
            structureType: project.structure_type,
            pacingPreference: project.pacing_preference,
            timePeriod: project.time_period,
            geographicSetting: project.geographic_setting,
            worldType: project.world_type,
            magicTechLevel: project.magic_tech_level,
            characterComplexity: 'complex_layered',
            projectScope: 'standalone',
            initialConcept: project.description || '',
            protagonistTypes: ['the_hero'],
            antagonistTypes: ['the_villain'],
            characterArcTypes: ['positive_change'],
            chapterStructure: 'fixed_length',
            timelineComplexity: 'linear',
            majorThemes: [],
            philosophicalThemes: [],
            socialThemes: [],
            contentWarnings: [],
            chapterCountType: 'flexible',
            povCharacterCount: 1,
            povCharacterType: 'single_pov',
            researchNeeds: [],
            factCheckingLevel: 'moderate'
          },
          characters: {
            protagonists: characters.filter(char => char.role === 'protagonist').map(char => ({
              id: char.id,
              name: char.name,
              role: char.role,
              appearance: char.physical_description || '',
              description: char.description,
              personality: {
                traits: char.personality_traits?.traits || [],
                strengths: char.personality_traits?.strengths || [],
                weaknesses: char.personality_traits?.weaknesses || [],
                fears: char.personality_traits?.fears || [],
                desires: char.personality_traits?.desires || []
              },
              backstory: char.backstory,
              motivation: char.personality_traits?.motivation || '',
              arc: char.character_arc || {
                type: 'positive_change',
                startingPoint: '',
                endingPoint: '',
                keyMoments: [],
                internalConflict: '',
                externalConflict: ''
              },
              voice: {
                speakingStyle: '',
                vocabulary: '',
                mannerisms: []
              },
              relationships: char.relationships || []
            })),
            antagonists: characters.filter(char => char.role === 'antagonist').map(char => ({
              id: char.id,
              name: char.name,
              role: char.role,
              appearance: char.physical_description || '',
              description: char.description,
              personality: {
                traits: char.personality_traits?.traits || [],
                strengths: char.personality_traits?.strengths || [],
                weaknesses: char.personality_traits?.weaknesses || [],
                fears: char.personality_traits?.fears || [],
                desires: char.personality_traits?.desires || []
              },
              backstory: char.backstory,
              motivation: char.personality_traits?.motivation || '',
              arc: char.character_arc || {
                type: 'negative_change',
                startingPoint: '',
                endingPoint: '',
                keyMoments: [],
                internalConflict: '',
                externalConflict: ''
              },
              voice: {
                speakingStyle: '',
                vocabulary: '',
                mannerisms: []
              },
              relationships: char.relationships || []
            })),
            supporting: characters.filter(char => ['supporting', 'minor'].includes(char.role)).map(char => ({
              id: char.id,
              name: char.name,
              role: char.role,
              appearance: char.physical_description || '',
              description: char.description,
              personality: {
                traits: char.personality_traits?.traits || [],
                strengths: char.personality_traits?.strengths || [],
                weaknesses: char.personality_traits?.weaknesses || [],
                fears: char.personality_traits?.fears || [],
                desires: char.personality_traits?.desires || []
              },
              backstory: char.backstory,
              motivation: char.personality_traits?.motivation || '',
              arc: char.character_arc || {
                type: 'flat_arc',
                startingPoint: '',
                endingPoint: '',
                keyMoments: [],
                internalConflict: '',
                externalConflict: ''
              },
              voice: {
                speakingStyle: '',
                vocabulary: '',
                mannerisms: []
              },
              relationships: char.relationships || []
            })),
            relationships: []
          },
          completedChapters: chapters?.map(ch => ({
            chapterNumber: ch.chapter_number,
            title: ch.title,
            content: ch.content,
            wordCount: ch.actual_word_count,
            scenes: [],
            characterVoices: [],
            themes: [],
            continuityNotes: []
          })) || [],
          storyBible: storyBible.length > 0 ? {
            projectId,
            lastUpdated: new Date(),
            structure: {
              title: project.title,
              premise: project.description || '',
              genre: project.primary_genre,
              themes: [],
              acts: storyArcs.map(arc => ({
                number: arc.act_number,
                title: `Act ${arc.act_number}`,
                description: arc.description,
                wordCountTarget: Math.round(project.target_word_count / 3),
                wordCount: 0, // Initialize to 0, will be updated as chapters are written
                chapters: [],
                keyEvents: arc.key_events || [],
                themes: [],
                characterArcs: []
              })),
              timeline: storyBible.filter(entry => entry.entry_type === 'timeline_event')
                .map(entry => ({
                  id: entry.entry_key,
                  title: entry.entry_data.title || entry.entry_data.event || '',
                  description: entry.entry_data.description || entry.entry_data.event || '',
                  event: entry.entry_data.event || entry.entry_data.title || '',
                  timestamp: entry.entry_data.timestamp || '',
                  actNumber: entry.entry_data.actNumber || 1,
                  chapterNumber: entry.entry_data.chapterNumber || entry.entry_data.chapter,
                  chapter: entry.entry_data.chapter || entry.entry_data.chapterNumber,
                  importance: (entry.entry_data.importance as 'critical' | 'major' | 'minor') || 'major'
                })),
              worldBuilding: {
                setting: {
                  timeForPeriod: project.time_period,
                  locations: [],
                  culture: '',
                  technology: project.magic_tech_level
                },
                rules: storyBible.filter(entry => entry.entry_type === 'world_rule')
                  .map(entry => entry.entry_data.value),
                history: []
              },
              conflicts: [],
              plotPoints: []
            },
            characters: characters.length > 0 ? {
              protagonists: characters.filter(c => c.role === 'protagonist').map(c => ({
                id: c.id,
                name: c.name,
                role: 'protagonist' as const,
                appearance: c.physical_description || '',
                personality: {
                  traits: c.personality_traits?.traits || [],
                  strengths: c.personality_traits?.strengths || [],
                  weaknesses: c.personality_traits?.weaknesses || [],
                  fears: c.personality_traits?.fears || [],
                  desires: c.personality_traits?.desires || []
                },
                backstory: c.backstory,
                motivation: c.personality_traits?.motivation || '',
                arc: c.character_arc || {
                  type: 'positive_change' as const,
                  startingPoint: '',
                  endingPoint: '',
                  keyMoments: [],
                  internalConflict: '',
                  externalConflict: ''
                },
                voice: {
                  speakingStyle: '',
                  vocabulary: '',
                  mannerisms: []
                },
                relationships: c.relationships || []
              })),
              antagonists: characters.filter(c => c.role === 'antagonist').map(c => ({
                id: c.id,
                name: c.name,
                role: 'antagonist' as const,
                appearance: c.physical_description || '',
                personality: {
                  traits: c.personality_traits?.traits || [],
                  strengths: c.personality_traits?.strengths || [],
                  weaknesses: c.personality_traits?.weaknesses || [],
                  fears: c.personality_traits?.fears || [],
                  desires: c.personality_traits?.desires || []
                },
                backstory: c.backstory,
                motivation: c.personality_traits?.motivation || '',
                arc: c.character_arc || {
                  type: 'negative_change' as const,
                  startingPoint: '',
                  endingPoint: '',
                  keyMoments: [],
                  internalConflict: '',
                  externalConflict: ''
                },
                voice: {
                  speakingStyle: '',
                  vocabulary: '',
                  mannerisms: []
                },
                relationships: c.relationships || []
              })),
              supporting: characters.filter(c => ['supporting', 'minor'].includes(c.role)).map(c => ({
                id: c.id,
                name: c.name,
                role: c.role as 'supporting' | 'minor',
                appearance: c.physical_description || '',
                personality: {
                  traits: c.personality_traits?.traits || [],
                  strengths: c.personality_traits?.strengths || [],
                  weaknesses: c.personality_traits?.weaknesses || [],
                  fears: c.personality_traits?.fears || [],
                  desires: c.personality_traits?.desires || []
                },
                backstory: c.backstory,
                motivation: c.personality_traits?.motivation || '',
                arc: c.character_arc || {
                  type: 'flat_arc' as const,
                  startingPoint: '',
                  endingPoint: '',
                  keyMoments: [],
                  internalConflict: '',
                  externalConflict: ''
                },
                voice: {
                  speakingStyle: '',
                  vocabulary: '',
                  mannerisms: []
                },
                relationships: c.relationships || []
              })),
              relationships: []
            } : {
              protagonists: [],
              antagonists: [],
              supporting: [],
              relationships: []
            },
            world: {
              setting: {
                timeForPeriod: project.time_period,
                locations: [],
                culture: '',
                technology: project.magic_tech_level
              },
              rules: storyBible.filter(entry => entry.entry_type === 'world_rule')
                .map(entry => entry.entry_data.value),
              history: []
            },
            timeline: storyBible.filter(entry => entry.entry_type === 'timeline_event')
              .map(entry => ({
                id: entry.entry_key,
                title: entry.entry_data.title || entry.entry_data.event || '',
                description: entry.entry_data.description || entry.entry_data.event || '',
                event: entry.entry_data.event || entry.entry_data.title || '',
                timestamp: entry.entry_data.timestamp || '',
                actNumber: entry.entry_data.actNumber || 1,
                chapterNumber: entry.entry_data.chapterNumber || entry.entry_data.chapter,
                chapter: entry.entry_data.chapter || entry.entry_data.chapterNumber,
                importance: (entry.entry_data.importance as 'critical' | 'major' | 'minor') || 'major'
              })),
            themes: [],
            continuity: {
              characterStates: new Map(),
              plotThreads: storyBible.filter(entry => entry.entry_type === 'plot_thread')
                .map(entry => ({
                  id: entry.entry_key,
                  name: entry.entry_data.name || entry.entry_key,
                  description: entry.entry_data.description,
                  status: entry.entry_data.status as 'active' | 'resolved' | 'abandoned',
                  introduction: 1,
                  relatedCharacters: []
                })),
              establishedFacts: []
            },
            style: {
              voice: project.narrative_voice,
              tone: project.tone_options || [],
              vocabulary: '',
              sentenceStructure: '',
              paragraphLength: '',
              dialogueStyle: '',
              descriptionStyle: ''
            }
          } : undefined,
          currentChapter: chapterNumber,
          metadata: {
            totalWordCount: project.current_word_count || 0,
            chaptersCompleted: chapters?.length || 0,
            lastUpdated: new Date().toISOString()
          }
        }

        // Find or create chapter outline
        const existingChapter = chapters?.find(ch => ch.chapter_number === chapterNumber)
        
        if (!existingChapter?.outline) {
          return NextResponse.json({ error: 'Chapter outline not found. Please generate project structure first.' }, { status: 400 })
        }

        const chapterOutline = JSON.parse(existingChapter.outline)
        
        // Get previous chapter for context (currently not used in orchestrator)
        // const previousChapter = chapters?.find(ch => ch.chapter_number === chapterNumber - 1)

        // Use the writing agent directly
        const writingAgent = new WritingAgent(context)
        const chapterContent = await writingAgent.writeChapter(chapterOutline)

        // Store the chapter in database
        await supabase
          .from('chapters')
          .update({
            content: chapterContent.content,
            actual_word_count: chapterContent.wordCount,
            status: 'complete',
            ai_notes: { characterVoices: chapterContent.characterVoices, continuityNotes: chapterContent.continuityNotes }
          })
          .eq('project_id', projectId)
          .eq('chapter_number', chapterNumber)

        // Update project word count
        await supabase
          .from('projects')
          .update({
            current_word_count: (project.current_word_count || 0) + chapterContent.wordCount
          })
          .eq('id', projectId)

        // Track usage
        await trackUsage({
          userId: user.id,
          eventType: 'ai_generation',
          metadata: { action: 'write_chapter', projectId, chapterNumber }
        })

        return NextResponse.json({
          success: true,
          data: chapterContent,
          message: `Chapter ${chapterNumber} written successfully`
        })
      }

      case 'generate_chapter_enhanced': {
        const { chapterNumber } = validationResult.data
        console.log(`Enhanced chapter generation for chapter ${chapterNumber}...`)

        // Get comprehensive project context
        const { data: storyArcs } = await supabase
          .from('story_arcs')
          .select('*')
          .eq('project_id', projectId)
          .order('act_number')

        const { data: characters } = await supabase
          .from('characters')
          .select('*')
          .eq('project_id', projectId)

        const { data: storyBible } = await supabase
          .from('story_bible')
          .select('*')
          .eq('project_id', projectId)

        const { data: chapters } = await supabase
          .from('chapters')
          .select('*')
          .eq('project_id', projectId)
          .order('chapter_number')

        if (!storyArcs || !characters || !storyBible || !chapters) {
          return NextResponse.json({ error: 'Project structure not found. Please generate structure first.' }, { status: 400 })
        }

        // Find target chapter
        const targetChapter = chapters.find(ch => ch.chapter_number === chapterNumber)
        if (!targetChapter?.outline) {
          return NextResponse.json({ error: 'Chapter outline not found. Please generate project structure first.' }, { status: 400 })
        }

        const chapterOutline = JSON.parse(targetChapter.outline)
        
        // Build enhanced chapter context
        const chapterContext: ChapterContext = {
          projectId,
          chapterNumber: chapterNumber ?? 1,
          chapterOutline,
          projectSettings: {
            primaryGenre: project.primary_genre,
            narrativeVoice: project.narrative_voice,
            tense: project.tense,
            writingStyle: project.writing_style,
            tone: project.tone_options || [],
            targetAudience: project.target_audience,
            contentRating: project.content_rating
          },
          characters: characters.map(char => ({
            id: char.id,
            name: char.name,
            role: char.role,
            description: char.description,
            personality: char.personality_traits,
            voiceCharacteristics: char.personality_traits?.voiceCharacteristics || []
          })),
          storyBible: {
            worldRules: Object.fromEntries(
              storyBible.filter(entry => entry.entry_type === 'world_rule')
                .map(entry => [entry.entry_key, entry.entry_data?.value || ''])
            ),
            timeline: storyBible.filter(entry => entry.entry_type === 'timeline_event')
              .map(entry => ({ 
                event: entry.entry_data?.event || '', 
                chapter: entry.entry_data?.chapter || 1 
              })),
            plotThreads: storyBible.filter(entry => entry.entry_type === 'plot_thread')
              .map(entry => ({
                id: entry.entry_key,
                description: entry.entry_data?.description || '',
                status: entry.entry_data?.status || 'active'
              }))
          },
          previousChapters: chapters
            .filter(ch => ch.chapter_number < (chapterNumber ?? 1) && ch.content)
            .map(ch => ({
              chapterNumber: ch.chapter_number,
              content: ch.content,
              wordCount: ch.actual_word_count || 0,
              characterVoices: ch.ai_notes?.characterVoices || {},
              summary: ch.ai_notes?.summary || `Chapter ${ch.chapter_number} content`
            }))
        }

        // Use enhanced chapter generator
        const chapterGenerator = new ChapterGenerator()
        await chapterGenerator.initialize()

        const result = await chapterGenerator.generateChapter(chapterContext)

        if (!result.success || !result.data) {
          console.error('Enhanced chapter generation failed:', result.error)
          return NextResponse.json({ error: result.error || 'Chapter generation failed' }, { status: 500 })
        }

        const generatedChapter = result.data

        // Store the generated chapter (but don't mark as complete yet - user needs to review)
        await supabase
          .from('chapters')
          .update({
            content: generatedChapter.content,
            actual_word_count: generatedChapter.wordCount,
            status: 'generated', // New status for AI-generated content awaiting review
            ai_notes: {
              characterVoices: generatedChapter.characterVoices,
              continuityNotes: generatedChapter.continuityNotes,
              plotProgression: generatedChapter.plotProgression,
              qualityScore: generatedChapter.qualityScore,
              generationMetadata: generatedChapter.generationMetadata,
              scenes: generatedChapter.scenes
            }
          })
          .eq('project_id', projectId)
          .eq('chapter_number', chapterNumber)

        // Track usage
        await trackUsage({
          userId: user.id,
          eventType: 'ai_generation',
          metadata: { action: 'generate_chapter_enhanced', projectId, chapterNumber }
        })

        return NextResponse.json({
          success: true,
          data: generatedChapter,
          message: `Chapter ${chapterNumber} generated successfully - ready for review`
        })
      }

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
  } catch (error) {
    console.error('Agent generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate content' },
      { status: 500 }
    )
  }
}