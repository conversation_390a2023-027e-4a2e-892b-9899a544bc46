'use client'

import { useEffect, useState } from 'react'
import { ThemeProvider } from '@/components/theme-provider'
import { Toaster } from '@/components/ui/toaster'
import { AuthProvider } from '@/contexts/auth-context'
import { KeyboardShortcutsProvider } from '@/components/ui/keyboard-shortcuts-provider'
import { ErrorHandlerInitializer } from '@/components/error/error-handler-initializer'
import { SettingsProvider } from '@/components/settings/settings-provider'

export function ClientProviders({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return <>{children}</>
  }

  return (
    <>
      <ThemeProvider
        attribute="class"
        defaultTheme="light"
        enableSystem
        disableTransitionOnChange
      >
        <SettingsProvider>
          <AuthProvider>
            <KeyboardShortcutsProvider>
              {children}
            </KeyboardShortcutsProvider>
          </AuthProvider>
        </SettingsProvider>
      </ThemeProvider>
      <Toaster />
      <ErrorHandlerInitializer />
    </>
  )
}
