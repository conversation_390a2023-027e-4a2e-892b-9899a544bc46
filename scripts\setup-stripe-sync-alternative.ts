#!/usr/bin/env tsx

/**
 * Alternative Stripe Sync Engine setup using Supabase client
 * This approach uses the Supabase client instead of direct database connections
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

async function setupStripeSyncAlternative() {
  console.log('🚀 Setting up Stripe Sync Engine (Alternative Method)...')
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY
  
  if (!supabaseUrl || !serviceRoleKey) {
    console.error('❌ Missing required environment variables')
    console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
    process.exit(1)
  }
  
  const supabase = createClient(supabaseUrl, serviceRoleKey)
  
  try {
    console.log('📊 Creating stripe schema and tables...')
    
    // Create the stripe schema
    console.log('1️⃣ Creating stripe schema...')
    const { error: schemaError } = await supabase.rpc('exec_sql', {
      sql: 'CREATE SCHEMA IF NOT EXISTS stripe;'
    })
    
    if (schemaError) {
      console.log('⚠️  Schema creation via RPC failed, trying direct SQL...')
      // Try alternative approach
      const { error: altSchemaError } = await supabase
        .from('_stripe_schema_setup')
        .insert({ setup: true })
        .then(() => ({ error: null }))
        .catch(() => ({ error: 'Could not create schema' }))
    } else {
      console.log('✅ Stripe schema created')
    }
    
    // Create essential tables manually
    const tables = [
      {
        name: 'customers',
        sql: `
          CREATE TABLE IF NOT EXISTS stripe.customers (
            id TEXT PRIMARY KEY,
            email TEXT,
            name TEXT,
            created BIGINT,
            updated BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
            metadata JSONB DEFAULT '{}',
            raw_data JSONB
          );
        `
      },
      {
        name: 'subscriptions',
        sql: `
          CREATE TABLE IF NOT EXISTS stripe.subscriptions (
            id TEXT PRIMARY KEY,
            customer TEXT,
            status TEXT,
            current_period_start BIGINT,
            current_period_end BIGINT,
            cancel_at_period_end BOOLEAN DEFAULT FALSE,
            created BIGINT,
            updated BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
            metadata JSONB DEFAULT '{}',
            raw_data JSONB
          );
        `
      },
      {
        name: 'invoices',
        sql: `
          CREATE TABLE IF NOT EXISTS stripe.invoices (
            id TEXT PRIMARY KEY,
            customer TEXT,
            subscription TEXT,
            status TEXT,
            amount_paid BIGINT DEFAULT 0,
            amount_due BIGINT DEFAULT 0,
            created BIGINT,
            updated BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
            metadata JSONB DEFAULT '{}',
            raw_data JSONB
          );
        `
      },
      {
        name: 'payment_intents',
        sql: `
          CREATE TABLE IF NOT EXISTS stripe.payment_intents (
            id TEXT PRIMARY KEY,
            customer TEXT,
            amount BIGINT,
            currency TEXT,
            status TEXT,
            created BIGINT,
            updated BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
            metadata JSONB DEFAULT '{}',
            raw_data JSONB
          );
        `
      },
      {
        name: 'products',
        sql: `
          CREATE TABLE IF NOT EXISTS stripe.products (
            id TEXT PRIMARY KEY,
            name TEXT,
            description TEXT,
            active BOOLEAN DEFAULT TRUE,
            created BIGINT,
            updated BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
            metadata JSONB DEFAULT '{}',
            raw_data JSONB
          );
        `
      },
      {
        name: 'prices',
        sql: `
          CREATE TABLE IF NOT EXISTS stripe.prices (
            id TEXT PRIMARY KEY,
            product TEXT,
            currency TEXT,
            unit_amount BIGINT,
            recurring JSONB,
            active BOOLEAN DEFAULT TRUE,
            created BIGINT,
            updated BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
            metadata JSONB DEFAULT '{}',
            raw_data JSONB
          );
        `
      }
    ]
    
    console.log('2️⃣ Creating stripe tables...')
    let successCount = 0
    
    for (const table of tables) {
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: table.sql })
        
        if (error) {
          console.log(`  ❌ ${table.name}: ${error.message}`)
        } else {
          console.log(`  ✅ ${table.name}`)
          successCount++
        }
      } catch (err) {
        console.log(`  ❌ ${table.name}: Error creating table`)
      }
    }
    
    console.log('3️⃣ Creating indexes for better performance...')
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_stripe_customers_email ON stripe.customers(email);',
      'CREATE INDEX IF NOT EXISTS idx_stripe_subscriptions_customer ON stripe.subscriptions(customer);',
      'CREATE INDEX IF NOT EXISTS idx_stripe_subscriptions_status ON stripe.subscriptions(status);',
      'CREATE INDEX IF NOT EXISTS idx_stripe_invoices_customer ON stripe.invoices(customer);',
      'CREATE INDEX IF NOT EXISTS idx_stripe_invoices_status ON stripe.invoices(status);',
      'CREATE INDEX IF NOT EXISTS idx_stripe_payment_intents_customer ON stripe.payment_intents(customer);',
      'CREATE INDEX IF NOT EXISTS idx_stripe_prices_product ON stripe.prices(product);'
    ]
    
    for (const indexSql of indexes) {
      try {
        await supabase.rpc('exec_sql', { sql: indexSql })
      } catch (err) {
        // Indexes are optional, continue if they fail
      }
    }
    
    console.log(`\n📊 Setup Summary:`)
    console.log(`   ✅ Successfully created ${successCount}/${tables.length} tables`)
    
    if (successCount > 0) {
      console.log('\n✅ Stripe Sync Engine setup completed successfully!')
      console.log('📝 The following has been created:')
      console.log('  - stripe schema in your database')
      console.log('  - Essential tables for Stripe data sync')
      console.log('  - Performance indexes')
      console.log('')
      console.log('🔗 Next steps:')
      console.log('  1. Set up Stripe webhooks to point to your sync endpoint:')
      console.log(`     ${process.env.NEXT_PUBLIC_APP_URL || 'https://your-domain.com'}/api/stripe/webhook`)
      console.log('  2. Configure your webhook secret in environment variables')
      console.log('  3. Add the webhook events you want to sync')
      console.log('  4. Start syncing Stripe data!')
      console.log('')
      console.log('📚 Webhook endpoint: /api/stripe/webhook')
      console.log('🔧 Admin dashboard: /admin/stripe')
    } else {
      console.log('\n❌ Setup failed - no tables were created')
      console.log('💡 This might be due to database permissions or RPC function availability')
      console.log('   Try running the original setup script or check your Supabase project settings')
    }
    
  } catch (error) {
    console.error('❌ Error setting up Stripe Sync Engine:', error)
    process.exit(1)
  }
}

setupStripeSyncAlternative().catch(console.error)
