#!/usr/bin/env tsx

/**
 * Test script to find the correct database connection format
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

async function testDatabaseConnection() {
  console.log('🔍 Testing Supabase database connection formats...')
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY
  
  if (!supabaseUrl || !serviceRoleKey) {
    console.error('❌ Missing required environment variables')
    console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
    process.exit(1)
  }
  
  console.log(`📍 Supabase URL: ${supabaseUrl}`)
  
  // Test Supabase client connection first
  console.log('\n1️⃣ Testing Supabase client connection...')
  try {
    const supabase = createClient(supabaseUrl, serviceRoleKey)
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .limit(1)
    
    if (error) {
      console.error('❌ Supabase client connection failed:', error.message)
    } else {
      console.log('✅ Supabase client connection successful')
    }
  } catch (error) {
    console.error('❌ Supabase client error:', error)
  }
  
  // Extract project reference
  const projectRef = supabaseUrl.split('//')[1]?.split('.')[0]
  console.log(`📋 Project reference: ${projectRef}`)
  
  // Test different database URL formats
  const testUrls = [
    `postgresql://postgres:${serviceRoleKey}@db.${projectRef}.supabase.co:5432/postgres`,
    `postgresql://postgres.${projectRef}:${serviceRoleKey}@aws-0-us-west-1.pooler.supabase.com:6543/postgres`,
    `postgresql://postgres:${serviceRoleKey}@${projectRef}.pooler.supabase.com:6543/postgres`,
    `postgresql://postgres:${serviceRoleKey}@db.${projectRef}.supabase.co:6543/postgres`,
  ]
  
  console.log('\n2️⃣ Testing direct database connections...')
  
  for (let i = 0; i < testUrls.length; i++) {
    const url = testUrls[i]
    const urlParts = url.split('@')[1]?.split('/')[0] // Extract hostname:port
    
    console.log(`\n🔄 Testing format ${i + 1}: ${urlParts}`)
    
    try {
      // Test hostname resolution first
      const { exec } = await import('child_process')
      const { promisify } = await import('util')
      const execAsync = promisify(exec)
      
      const hostname = urlParts?.split(':')[0]
      if (hostname) {
        try {
          await execAsync(`nslookup ${hostname}`)
          console.log(`  ✅ Hostname ${hostname} resolves`)
        } catch (dnsError) {
          console.log(`  ❌ Hostname ${hostname} does not resolve`)
          continue
        }
      }
      
      // If hostname resolves, this format might work
      console.log(`  📝 Database URL format ${i + 1} might work:`)
      console.log(`     ${url.replace(serviceRoleKey, '[SERVICE_ROLE_KEY]')}`)
      
    } catch (error) {
      console.log(`  ❌ Format ${i + 1} test failed:`, error instanceof Error ? error.message : 'Unknown error')
    }
  }
  
  console.log('\n💡 Recommendations:')
  console.log('1. Use the Supabase client for most operations (it works)')
  console.log('2. For direct database connections, check your Supabase project settings')
  console.log('3. Go to: Project Settings > Database > Connection string')
  console.log('4. Use the "Direct connection" or "Connection pooling" string')
  console.log('5. Replace [YOUR-PASSWORD] with your service role key')
}

testDatabaseConnection().catch(console.error)
