'use client'

import { ReactNode, Suspense } from 'react'
import { Toaster } from '@/components/ui/toaster'
import { ProviderErrorBoundary } from '@/components/error-boundaries/provider-error-boundary'

interface NotificationsProviderProps {
  children: ReactNode
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  duration?: number
}

function NotificationsProviderCore({ 
  children, 
  position = 'top-right',
  duration = 5000 
}: NotificationsProviderProps) {
  return (
    <>
      {children}
      <Toaster />
    </>
  )
}

export default function NotificationsMicroProvider({ children, ...props }: NotificationsProviderProps) {
  return (
    <ProviderErrorBoundary 
      providerName="Notifications Provider"
      fallback={children}
    >
      <Suspense fallback={children}>
        <NotificationsProviderCore {...props}>
          {children}
        </NotificationsProviderCore>
      </Suspense>
    </ProviderErrorBoundary>
  )
}
