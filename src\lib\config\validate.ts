import { config } from './index';

/**
 * Validates environment variables on startup
 * This should be called in instrumentation.ts or at the start of the app
 */
export function validateEnvironment(): void {
  try {
    // Access config to trigger validation
    // Validate environment access
    config.env;
    console.log('✅ Environment variables validated successfully');
  } catch (error) {
    console.error('❌ Environment validation failed:', error);
    if (error instanceof Error) {
      console.error('Missing or invalid environment variables. Please check your .env file.');
      console.error(error.message);
    }
    // In production, we should fail fast
    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    }
  }
}

/**
 * Checks if all required services are properly configured
 */
export function validateServices(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  try {
    // Check Supabase configuration
    if (!config.supabase.url || !config.supabase.anonKey) {
      errors.push('Supabase configuration is incomplete');
    }

    // Check OpenAI configuration
    if (!config.openai.apiKey) {
      errors.push('OpenAI API key is missing');
    }

    // Check Stripe configuration
    if (!config.stripe.secretKey || !config.stripe.publishableKey) {
      errors.push('Stripe configuration is incomplete');
    }

    // Check Stripe price IDs
    if (!config.stripe.prices.basic || !config.stripe.prices.pro || !config.stripe.prices.enterprise) {
      errors.push('Stripe price IDs are not configured');
    }

    // Check app URL
    if (!config.app.url) {
      errors.push('App URL is not configured');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  } catch (error) {
    return {
      valid: false,
      errors: ['Failed to validate configuration: ' + (error instanceof Error ? error.message : 'Unknown error')]
    };
  }
}

/**
 * Logs the current configuration (with sensitive values masked)
 */
export function logConfiguration(): void {
  console.log('Current configuration:');
  console.log('- Environment:', process.env.NODE_ENV);
  console.log('- App URL:', config.app.url);
  console.log('- Supabase URL:', config.supabase.url);
  console.log('- OpenAI configured:', !!config.openai.apiKey);
  console.log('- Stripe configured:', !!config.stripe.secretKey);
  console.log('- Stripe prices configured:', !!config.stripe.prices.basic);
}