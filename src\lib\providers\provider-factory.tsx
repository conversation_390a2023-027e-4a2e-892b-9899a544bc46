import { ReactNode, ComponentType } from 'react'

export interface ProviderConfig {
  theme?: {
    enabled: boolean
    defaultTheme?: string
    enableSystem?: boolean
    storageKey?: string
  }
  auth?: {
    enabled: boolean
    redirectTo?: string
    autoRefresh?: boolean
  }
  settings?: {
    enabled: boolean
    persistToStorage?: boolean
    storageKey?: string
  }
  notifications?: {
    enabled: boolean
    position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
    duration?: number
  }
  keyboard?: {
    enabled: boolean
    shortcuts?: Record<string, () => void>
  }
  errorHandling?: {
    enabled: boolean
    reportToSentry?: boolean
    showErrorUI?: boolean
  }
}

export interface ProviderModule {
  component: ComponentType<{ children: ReactNode }>
  name: string
  priority: number
  dependencies?: string[]
  fallback?: ComponentType
}

export class ProviderFactory {
  private static instance: ProviderFactory
  private modules: Map<string, ProviderModule> = new Map()
  private config: ProviderConfig = {}

  static getInstance(): ProviderFactory {
    if (!ProviderFactory.instance) {
      ProviderFactory.instance = new ProviderFactory()
    }
    return ProviderFactory.instance
  }

  configure(config: ProviderConfig): this {
    this.config = { ...this.config, ...config }
    return this
  }

  register(name: string, module: ProviderModule): this {
    this.modules.set(name, module)
    return this
  }

  getEnabledProviders(): ProviderModule[] {
    const enabled: ProviderModule[] = []

    // Check each provider type
    if (this.config.theme?.enabled && this.modules.has('theme')) {
      enabled.push(this.modules.get('theme')!)
    }
    
    if (this.config.auth?.enabled && this.modules.has('auth')) {
      enabled.push(this.modules.get('auth')!)
    }
    
    if (this.config.settings?.enabled && this.modules.has('settings')) {
      enabled.push(this.modules.get('settings')!)
    }
    
    if (this.config.notifications?.enabled && this.modules.has('notifications')) {
      enabled.push(this.modules.get('notifications')!)
    }
    
    if (this.config.keyboard?.enabled && this.modules.has('keyboard')) {
      enabled.push(this.modules.get('keyboard')!)
    }
    
    if (this.config.errorHandling?.enabled && this.modules.has('errorHandling')) {
      enabled.push(this.modules.get('errorHandling')!)
    }

    // Sort by priority (higher priority = outer wrapper)
    return enabled.sort((a, b) => b.priority - a.priority)
  }

  createProviderTree(): ComponentType<{ children: ReactNode }> {
    const providers = this.getEnabledProviders()
    
    return function ProviderTree({ children }: { children: ReactNode }) {
      return providers.reduce(
        (acc, provider) => {
          const Provider = provider.component
          return <Provider>{acc}</Provider>
        },
        children as ReactNode
      )
    }
  }

  getConfig(): ProviderConfig {
    return this.config
  }
}

// Default configuration
export const defaultProviderConfig: ProviderConfig = {
  theme: {
    enabled: true,
    defaultTheme: 'light',
    enableSystem: false,
    storageKey: 'bookscribe-theme'
  },
  auth: {
    enabled: false, // Start disabled, enable when needed
    redirectTo: '/login',
    autoRefresh: true
  },
  settings: {
    enabled: false, // Start disabled, enable when needed
    persistToStorage: true,
    storageKey: 'bookscribe-settings'
  },
  notifications: {
    enabled: true,
    position: 'top-right',
    duration: 5000
  },
  keyboard: {
    enabled: false, // Start disabled, enable when needed
    shortcuts: {}
  },
  errorHandling: {
    enabled: true,
    reportToSentry: true,
    showErrorUI: true
  }
}
