'use client'

import { ReactNode, Suspense } from 'react'
import { ProviderErrorBoundary } from '@/components/error-boundaries/provider-error-boundary'

interface AuthProviderProps {
  children: ReactNode
  redirectTo?: string
  autoRefresh?: boolean
}

function AuthProviderCore({ 
  children, 
  redirectTo = '/login',
  autoRefresh = true 
}: AuthProviderProps) {
  // Lazy load the actual auth context to avoid circular dependencies
  const AuthContext = require('@/contexts/auth-context').AuthProvider
  
  return (
    <AuthContext redirectTo={redirectTo} autoRefresh={autoRefresh}>
      {children}
    </AuthContext>
  )
}

export default function AuthMicroProvider({ children, ...props }: AuthProviderProps) {
  return (
    <ProviderErrorBoundary 
      providerName="Auth Provider"
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">Authentication Unavailable</h2>
            <p className="text-muted-foreground">Continue without authentication</p>
            <div className="mt-4">
              {children}
            </div>
          </div>
        </div>
      }
    >
      <Suspense fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-pulse">Loading authentication...</div>
        </div>
      }>
        <AuthProviderCore {...props}>
          {children}
        </AuthProviderCore>
      </Suspense>
    </ProviderErrorBoundary>
  )
}
