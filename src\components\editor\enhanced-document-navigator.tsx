'use client';

import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/lib/supabase/client';
import { useEditorStore } from '@/stores/editor-store';
import { DocumentTree } from './document-tree';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge as _Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  PanelLeftClose, 
  PanelLeftOpen, 
  BookOpen, 
  Target, 
  TrendingUp as _TrendingUp,
  FolderOpen,
  Plus as _Plus
} from 'lucide-react';
import type { DocumentNode, DocumentNodeType } from '@/types/document-tree';
import { Database } from '@/lib/db/types';

type Chapter = Database['public']['Tables']['chapters']['Row'];
type Project = Database['public']['Tables']['projects']['Row'];

interface EnhancedDocumentNavigatorProps {
  projectId: string;
  currentChapterId?: string;
  content?: string;
  onChapterSelect: (chapterId: string, chapterNumber: number) => void;
  onCreateChapter: () => void;
  onImportComplete?: () => void;
}

export function EnhancedDocumentNavigator({
  projectId,
  currentChapterId,
  content: _content,
  onChapterSelect,
  onCreateChapter: _onCreateChapter,
  onImportComplete: _onImportComplete,
}: EnhancedDocumentNavigatorProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState('chapters');
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [project, setProject] = useState<Project | null>(null);
  const [documentNodes, setDocumentNodes] = useState<DocumentNode[]>([]);
  const [loading, setLoading] = useState(true);

  const { updateChapterList } = useEditorStore();
  const supabase = createClient();

  // Load project and chapters
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // Load project
        const { data: projectData } = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectId)
          .single();

        if (projectData) {
          setProject(projectData);
        }

        // Load chapters
        const { data: chaptersData } = await supabase
          .from('chapters')
          .select('*')
          .eq('project_id', projectId)
          .order('chapter_number');

        if (chaptersData) {
          setChapters(chaptersData);
          updateChapterList(chaptersData);
          
          // Convert chapters to document nodes
          const nodes: DocumentNode[] = chaptersData.map((chapter, index) => ({
            id: chapter.id,
            title: chapter.title || `Chapter ${chapter.chapter_number}`,
            type: 'chapter' as DocumentNodeType,
            order: index,
            chapterNumber: chapter.chapter_number,
            wordCount: chapter.actual_word_count || 0,
            targetWordCount: chapter.target_word_count || undefined,
            status: (chapter.status as 'draft' | 'in-progress' | 'completed' | 'published') || 'draft',
            lastModified: new Date(chapter.updated_at),
          }));

          setDocumentNodes(nodes);
        }
      } catch (error) {
        console.error('Error loading project data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [projectId, supabase, updateChapterList]);

  // Calculate project stats
  const projectStats = {
    totalWords: chapters.reduce((sum, ch) => sum + (ch.actual_word_count || 0), 0),
    targetWords: project?.target_word_count || 0,
    completedChapters: chapters.filter(ch => ch.status === 'completed').length,
    totalChapters: chapters.length,
  };

  const progressPercentage = projectStats.targetWords > 0 
    ? Math.round((projectStats.totalWords / projectStats.targetWords) * 100)
    : 0;

  const handleNodeSelect = useCallback((node: DocumentNode) => {
    if (node.type === 'chapter') {
      onChapterSelect(node.id, node.chapterNumber || 1);
    }
  }, [onChapterSelect]);

  const handleNodeCreate = useCallback(async (node: DocumentNode) => {
    if (node.type === 'chapter') {
      try {
        const maxChapterNumber = Math.max(...chapters.map(ch => ch.chapter_number), 0);
        const { data, error } = await supabase
          .from('chapters')
          .insert({
            project_id: projectId,
            chapter_number: maxChapterNumber + 1,
            title: node.title,
            content: '',
            status: 'writing',
            target_word_count: 3000, // Default target
          })
          .select()
          .single();

        if (data && !error) {
          const newChapter: Chapter = data;
          setChapters(prev => [...prev, newChapter]);
          
          // Update document nodes
          const newNode: DocumentNode = {
            id: newChapter.id,
            title: newChapter.title || `Chapter ${newChapter.chapter_number}`,
            type: 'chapter',
            order: chapters.length,
            chapterNumber: newChapter.chapter_number,
            wordCount: 0,
            targetWordCount: newChapter.target_word_count || undefined,
            status: 'draft',
            lastModified: new Date(),
          };
          
          setDocumentNodes(prev => prev.filter(n => n.id !== node.id).concat(newNode));
          onChapterSelect(newChapter.id, newChapter.chapter_number);
        }
      } catch (error) {
        console.error('Error creating chapter:', error);
      }
    }
  }, [chapters, projectId, supabase, onChapterSelect]);

  const handleNodeUpdate = useCallback(async (node: DocumentNode) => {
    if (node.type === 'chapter') {
      try {
        const { error } = await supabase
          .from('chapters')
          .update({
            title: node.title,
            status: node.status,
            target_word_count: node.targetWordCount,
          })
          .eq('id', node.id);

        if (!error) {
          setChapters(prev => prev.map(ch => 
            ch.id === node.id 
              ? { ...ch, title: node.title, status: node.status as string }
              : ch
          ));
        }
      } catch (error) {
        console.error('Error updating chapter:', error);
      }
    }
  }, [supabase]);

  const handleNodeDelete = useCallback(async (nodeId: string) => {
    try {
      const { error } = await supabase
        .from('chapters')
        .delete()
        .eq('id', nodeId);

      if (!error) {
        setChapters(prev => prev.filter(ch => ch.id !== nodeId));
        setDocumentNodes(prev => prev.filter(n => n.id !== nodeId));
      }
    } catch (error) {
      console.error('Error deleting chapter:', error);
    }
  }, [supabase]);

  if (loading) {
    return (
      <Card className="w-80 h-full">
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-8 bg-muted rounded"></div>
            <div className="space-y-2">
              {[1, 2, 3, 4, 5].map(i => (
                <div key={i} className="h-6 bg-muted rounded"></div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`h-full transition-all duration-300 ${isCollapsed ? 'w-12' : 'w-80'}`}>
      {isCollapsed ? (
        <div className="p-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(false)}
            className="w-full"
          >
            <PanelLeftOpen className="w-4 h-4" />
          </Button>
        </div>
      ) : (
        <>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <h2 className="font-semibold text-lg">Project Navigator</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsCollapsed(true)}
              >
                <PanelLeftClose className="w-4 h-4" />
              </Button>
            </div>

            {/* Project Stats */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Progress</span>
                <span className="font-medium">
                  {projectStats.totalWords.toLocaleString()} / {projectStats.targetWords.toLocaleString()} words
                </span>
              </div>
              <Progress value={progressPercentage} className="h-2" />
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>{progressPercentage}% complete</span>
                <span>
                  {projectStats.completedChapters} / {projectStats.totalChapters} chapters
                </span>
              </div>
            </div>
          </CardHeader>

          <CardContent className="flex-1 p-0">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-3 mx-3">
                <TabsTrigger value="chapters" className="text-xs">
                  <BookOpen className="w-3 h-3 mr-1" />
                  Chapters
                </TabsTrigger>
                <TabsTrigger value="outline" className="text-xs">
                  <FolderOpen className="w-3 h-3 mr-1" />
                  Outline
                </TabsTrigger>
                <TabsTrigger value="research" className="text-xs">
                  <Target className="w-3 h-3 mr-1" />
                  Research
                </TabsTrigger>
              </TabsList>

              <div className="flex-1 mt-3">
                <TabsContent value="chapters" className="h-full m-0">
                  <DocumentTree
                    projectId={projectId}
                    initialNodes={documentNodes}
                    selectedNodeId={currentChapterId}
                    onNodeSelect={handleNodeSelect}
                    onNodeCreate={handleNodeCreate}
                    onNodeUpdate={handleNodeUpdate}
                    onNodeDelete={handleNodeDelete}
                    showWordCounts={true}
                    showStatus={true}
                    allowDragDrop={true}
                    readonly={false}
                  />
                </TabsContent>

                <TabsContent value="outline" className="h-full m-0">
                  <div className="p-3 h-full">
                    <div className="text-center text-muted-foreground py-8">
                      <FolderOpen className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">Outline view coming soon</p>
                      <p className="text-xs">Organize your story structure</p>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="research" className="h-full m-0">
                  <div className="p-3 h-full">
                    <div className="text-center text-muted-foreground py-8">
                      <Target className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">Research hub coming soon</p>
                      <p className="text-xs">Manage references and notes</p>
                    </div>
                  </div>
                </TabsContent>
              </div>
            </Tabs>
          </CardContent>
        </>
      )}
    </Card>
  );
}

export default EnhancedDocumentNavigator;