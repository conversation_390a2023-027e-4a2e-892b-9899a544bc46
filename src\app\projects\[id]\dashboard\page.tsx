'use client';

import { useEffect, useState } from 'react';
import { createClient } from '@/lib/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { BookOpen, Edit3, BarChart3, Users, Calendar, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { Database } from '@/lib/db/types';

type Project = Database['public']['Tables']['projects']['Row'];
type Chapter = Database['public']['Tables']['chapters']['Row'];

interface ProjectDashboardPageProps {
  params: {
    id: string;
  };
}

export default function ProjectDashboardPage({ params }: ProjectDashboardPageProps) {
  const [project, setProject] = useState<Project | null>(null);
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [loading, setLoading] = useState(true);
  const supabase = createClient();

  useEffect(() => {
    async function loadProjectData() {
      try {
        // Load project details
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('*')
          .eq('id', params.id)
          .single();

        if (projectError) throw projectError;
        setProject(projectData);

        // Load chapters
        const { data: chaptersData, error: chaptersError } = await supabase
          .from('chapters')
          .select('*')
          .eq('project_id', params.id)
          .order('chapter_number');

        if (chaptersError) throw chaptersError;
        setChapters(chaptersData || []);
      } catch (error) {
        console.error('Error loading project data:', error);
      } finally {
        setLoading(false);
      }
    }

    loadProjectData();
  }, [params.id, supabase]);

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">Loading project dashboard...</div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">Project not found</div>
      </div>
    );
  }

  const completedChapters = chapters.filter(ch => ch.status === 'completed').length;
  const totalChapters = chapters.length;
  const progressPercentage = totalChapters > 0 ? (completedChapters / totalChapters) * 100 : 0;
  const totalWords = chapters.reduce((sum, ch) => sum + (ch.word_count || 0), 0);

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <Link href="/dashboard">
          <Button variant="ghost" className="mb-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">{project.title}</h1>
        <p className="text-muted-foreground mt-2">{project.description}</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Words</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalWords.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Chapters</CardTitle>
            <Edit3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedChapters}/{totalChapters}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Progress</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(progressPercentage)}%</div>
            <Progress value={progressPercentage} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge variant={project.status === 'completed' ? 'default' : 'secondary'}>
              {project.status}
            </Badge>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks for this project</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Link href={`/projects/${params.id}/write`}>
              <Button className="w-full">
                <Edit3 className="mr-2 h-4 w-4" />
                Continue Writing
              </Button>
            </Link>
            <Link href={`/projects/${params.id}/chapters`}>
              <Button variant="outline" className="w-full">
                <BookOpen className="mr-2 h-4 w-4" />
                Manage Chapters
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Chapters</CardTitle>
            <CardDescription>Your latest work</CardDescription>
          </CardHeader>
          <CardContent>
            {chapters.length > 0 ? (
              <div className="space-y-2">
                {chapters.slice(-5).reverse().map((chapter) => (
                  <div key={chapter.id} className="flex justify-between items-center p-2 border rounded">
                    <div>
                      <div className="font-medium">{chapter.title}</div>
                      <div className="text-sm text-muted-foreground">
                        {chapter.word_count || 0} words
                      </div>
                    </div>
                    <Badge variant={chapter.status === 'completed' ? 'default' : 'secondary'}>
                      {chapter.status}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">No chapters yet</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}