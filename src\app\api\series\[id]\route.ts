import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { supabase } from '@/lib/db/client';

// Series book interface
interface SeriesBook {
  id: string;
  name: string;
  description?: string;
  status: string;
  word_count: number;
  target_word_count: number;
  created_at: string;
  completed_at?: string;
  series_order: number;
}

interface SeriesUpdateData {
  name?: string;
  description?: string;
  genre?: string;
  planned_books?: number;
  status?: string;
  series_type?: string;
  connection_level?: string;
  shared_universe?: string;
  tags?: string[];
  updated_at: string;
}

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: seriesId } = await params;

    const { data: series, error } = await supabase
      .from('book_series')
      .select(`
        *,
        books:projects!series_id(
          id,
          name,
          description,
          status,
          word_count,
          target_word_count,
          created_at,
          completed_at,
          series_order
        )
      `)
      .eq('id', seriesId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Series not found' }, { status: 404 });
      }
      throw error;
    }

    const books = series.books || [];
    const totalWordCount = books.reduce((sum: number, book: SeriesBook) => sum + (book.word_count || 0), 0);
    const targetWordCount = books.reduce((sum: number, book: SeriesBook) => sum + (book.target_word_count || 0), 0);
    const completedBooks = books.filter((book: SeriesBook) => book.status === 'completed').length;

    const formattedSeries = {
      id: series.id,
      name: series.name,
      description: series.description,
      genre: series.genre,
      status: series.status,
      plannedBooks: series.planned_books,
      currentBooks: books.length,
      completedBooks,
      totalWordCount,
      targetWordCount,
      seriesType: series.series_type,
      connectionLevel: series.connection_level,
      universe: series.shared_universe,
      tags: series.tags || [],
      createdAt: new Date(series.created_at),
      updatedAt: new Date(series.updated_at),
      books: books.map((book: SeriesBook) => ({
        id: book.id,
        name: book.name,
        description: book.description,
        status: book.status,
        wordCount: book.word_count,
        targetWordCount: book.target_word_count,
        order: book.series_order,
        createdAt: new Date(book.created_at),
        completedAt: book.completed_at ? new Date(book.completed_at) : null
      })).sort((a: { order: number }, b: { order: number }) => a.order - b.order)
    };

    return NextResponse.json({ series: formattedSeries });
  } catch (error) {
    console.error('Series detail API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: seriesId } = await params;
    const body = await request.json();
    const {
      name,
      description,
      genre,
      plannedBooks,
      status,
      seriesType,
      connectionLevel,
      sharedUniverse,
      tags
    } = body;

    const updates: SeriesUpdateData = {} as SeriesUpdateData;
    
    if (name !== undefined) updates.name = name;
    if (description !== undefined) updates.description = description;
    if (genre !== undefined) updates.genre = genre;
    if (plannedBooks !== undefined) updates.planned_books = plannedBooks;
    if (status !== undefined) updates.status = status;
    if (seriesType !== undefined) updates.series_type = seriesType;
    if (connectionLevel !== undefined) updates.connection_level = connectionLevel;
    if (sharedUniverse !== undefined) updates.shared_universe = sharedUniverse;
    if (tags !== undefined) updates.tags = tags;
    
    updates.updated_at = new Date().toISOString();

    const { data: series, error } = await supabase
      .from('book_series')
      .update(updates)
      .eq('id', seriesId)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Series not found' }, { status: 404 });
      }
      throw error;
    }

    const formattedSeries = {
      id: series.id,
      name: series.name,
      description: series.description,
      genre: series.genre,
      status: series.status,
      plannedBooks: series.planned_books,
      seriesType: series.series_type,
      connectionLevel: series.connection_level,
      universe: series.shared_universe,
      tags: series.tags || [],
      updatedAt: new Date(series.updated_at)
    };

    return NextResponse.json({ series: formattedSeries });
  } catch (error) {
    console.error('Series update API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: seriesId } = await params;

    // Check if series has any books
    const { data: books, error: booksError } = await supabase
      .from('projects')
      .select('id')
      .eq('series_id', seriesId);

    if (booksError) throw booksError;

    if (books && books.length > 0) {
      return NextResponse.json({ 
        error: 'Cannot delete series with existing books. Remove books from series first.' 
      }, { status: 400 });
    }

    // Delete the series
    const { error: deleteError } = await supabase
      .from('book_series')
      .delete()
      .eq('id', seriesId);

    if (deleteError) {
      if (deleteError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Series not found' }, { status: 404 });
      }
      throw deleteError;
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Series deletion API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}