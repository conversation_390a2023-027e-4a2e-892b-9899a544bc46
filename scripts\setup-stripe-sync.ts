#!/usr/bin/env tsx

/**
 * Setup script for Stripe Sync Engine
 * This script creates the necessary database schema and tables for syncing Stripe data
 */

import { runMigrations } from '@supabase/stripe-sync-engine'
import { config } from '../src/lib/config'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

async function setupStripeSync() {
  console.log('🚀 Setting up Stripe Sync Engine...')

  try {
    // Validate required environment variables
    if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error('SUPABASE_SERVICE_ROLE_KEY is required')
    }

    if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
      throw new Error('NEXT_PUBLIC_SUPABASE_URL is required')
    }

    // Extract project ref from Supabase URL
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const projectRef = supabaseUrl.split('//')[1]?.split('.')[0]

    if (!projectRef) {
      throw new Error('Could not extract project ref from NEXT_PUBLIC_SUPABASE_URL')
    }

    // Construct database URL
    const databaseUrl = `postgresql://postgres:${process.env.SUPABASE_SERVICE_ROLE_KEY}@db.${projectRef}.supabase.co:5432/postgres`

    console.log('📊 Running database migrations...')
    console.log(`🔗 Connecting to project: ${projectRef}`)

    await runMigrations({
      databaseUrl,
      schema: 'stripe',
      logger: console,
    })

    console.log('✅ Stripe Sync Engine setup completed successfully!')
    console.log('📝 The following has been created:')
    console.log('  - stripe schema in your database')
    console.log('  - All necessary tables for Stripe data sync')
    console.log('')
    console.log('🔗 Next steps:')
    console.log('  1. Set up Stripe webhooks to point to your sync endpoint:')
    console.log(`     ${process.env.NEXT_PUBLIC_APP_URL || 'https://your-domain.com'}/api/stripe/webhook`)
    console.log('  2. Configure your webhook secret in environment variables')
    console.log('  3. Add the webhook events you want to sync')
    console.log('  4. Start syncing Stripe data!')
    console.log('')
    console.log('📚 Webhook endpoint: /api/stripe/webhook')
    console.log('🔧 Admin dashboard: /admin/stripe')

  } catch (error) {
    console.error('❌ Error setting up Stripe Sync Engine:', error)
    process.exit(1)
  }
}

// Run the setup
setupStripeSync()
