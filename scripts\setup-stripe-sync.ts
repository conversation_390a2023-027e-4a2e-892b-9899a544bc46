#!/usr/bin/env tsx

/**
 * Setup script for Stripe Sync Engine
 * This script creates the necessary database schema and tables for syncing Stripe data
 */

import { runMigrations } from '@supabase/stripe-sync-engine'
import { config } from '../src/lib/config'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

async function setupStripeSync() {
  console.log('🚀 Setting up Stripe Sync Engine...')

  try {
    // Validate required environment variables
    if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error('SUPABASE_SERVICE_ROLE_KEY is required')
    }

    if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
      throw new Error('NEXT_PUBLIC_SUPABASE_URL is required')
    }

    // Extract project ref from Supabase URL
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const projectRef = supabaseUrl.split('//')[1]?.split('.')[0]

    if (!projectRef) {
      throw new Error('Could not extract project ref from NEXT_PUBLIC_SUPABASE_URL')
    }

    // Try different database URL formats (based on connection test results)
    const databaseUrls = [
      // Direct connection (port 5432)
      `postgresql://postgres:${process.env.SUPABASE_SERVICE_ROLE_KEY}@db.${projectRef}.supabase.co:5432/postgres`,
      // Pooler connection (alternative format)
      `postgresql://postgres:${process.env.SUPABASE_SERVICE_ROLE_KEY}@${projectRef}.pooler.supabase.com:6543/postgres`,
      // Direct connection (port 6543)
      `postgresql://postgres:${process.env.SUPABASE_SERVICE_ROLE_KEY}@db.${projectRef}.supabase.co:6543/postgres`,
      // Pooler with username format
      `postgresql://postgres.${projectRef}:${process.env.SUPABASE_SERVICE_ROLE_KEY}@aws-0-us-west-1.pooler.supabase.com:6543/postgres`,
    ]

    let databaseUrl = databaseUrls[0] // Start with direct connection

    console.log('📊 Running database migrations...')
    console.log(`🔗 Connecting to project: ${projectRef}`)

    // Try different connection formats until one works
    let migrationSuccess = false
    let lastError: Error | null = null

    for (let i = 0; i < databaseUrls.length; i++) {
      const currentUrl = databaseUrls[i]
      const maskedUrl = currentUrl.replace(process.env.SUPABASE_SERVICE_ROLE_KEY!, '[SERVICE_ROLE_KEY]')

      console.log(`🔄 Trying connection format ${i + 1}/${databaseUrls.length}...`)
      console.log(`   ${maskedUrl}`)

      try {
        await runMigrations({
          databaseUrl: currentUrl,
          schema: 'stripe',
          logger: {
            ...console,
            // Reduce noise from migration logs
            log: (message: string) => {
              if (message.includes('Running migration') || message.includes('Migration completed')) {
                console.log(`   ${message}`)
              }
            }
          },
        })
        migrationSuccess = true
        databaseUrl = currentUrl
        console.log(`✅ Connection format ${i + 1} successful!`)
        break
      } catch (error) {
        lastError = error as Error
        const errorMsg = error instanceof Error ? error.message : 'Unknown error'
        console.log(`❌ Connection format ${i + 1} failed: ${errorMsg}`)

        // Log specific error types for debugging
        if (errorMsg.includes('ENOTFOUND')) {
          console.log('   → DNS resolution failed')
        } else if (errorMsg.includes('ECONNREFUSED')) {
          console.log('   → Connection refused')
        } else if (errorMsg.includes('authentication')) {
          console.log('   → Authentication failed')
        } else if (errorMsg.includes('timeout')) {
          console.log('   → Connection timeout')
        }

        if (i < databaseUrls.length - 1) {
          console.log('🔄 Trying next connection format...')
        }
      }
    }

    if (!migrationSuccess) {
      console.log('\n❌ All connection formats failed!')
      console.log('💡 Possible solutions:')
      console.log('   1. Check your SUPABASE_SERVICE_ROLE_KEY is correct')
      console.log('   2. Verify your Supabase project is active')
      console.log('   3. Try setting DATABASE_URL manually in .env.local')
      console.log('   4. Check Supabase project settings for the correct connection string')
      throw lastError || new Error('All connection formats failed')
    }

    console.log('✅ Stripe Sync Engine setup completed successfully!')
    console.log('📝 The following has been created:')
    console.log('  - stripe schema in your database')
    console.log('  - All necessary tables for Stripe data sync')
    console.log('')
    console.log('🔗 Next steps:')
    console.log('  1. Set up Stripe webhooks to point to your sync endpoint:')
    console.log(`     ${process.env.NEXT_PUBLIC_APP_URL || 'https://your-domain.com'}/api/stripe/webhook`)
    console.log('  2. Configure your webhook secret in environment variables')
    console.log('  3. Add the webhook events you want to sync')
    console.log('  4. Start syncing Stripe data!')
    console.log('')
    console.log('📚 Webhook endpoint: /api/stripe/webhook')
    console.log('🔧 Admin dashboard: /admin/stripe')

  } catch (error) {
    console.error('❌ Error setting up Stripe Sync Engine:', error)
    process.exit(1)
  }
}

// Run the setup
setupStripeSync()
