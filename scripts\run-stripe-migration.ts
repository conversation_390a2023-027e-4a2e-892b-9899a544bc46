#!/usr/bin/env tsx

/**
 * Run Stripe Sync Engine SQL migration
 */

import { createClient } from '@supabase/supabase-js'
import { readFileSync } from 'fs'
import { join } from 'path'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

async function runStripeMigration() {
  console.log('🚀 Running Stripe Sync Engine SQL migration...')
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY
  
  if (!supabaseUrl || !serviceRoleKey) {
    console.error('❌ Missing required environment variables')
    console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
    process.exit(1)
  }
  
  const supabase = createClient(supabaseUrl, serviceRoleKey)
  
  try {
    // Read the SQL migration file
    const migrationPath = join(process.cwd(), 'supabase', 'migrations', 'stripe_sync_schema.sql')
    const migrationSQL = readFileSync(migrationPath, 'utf-8')
    
    console.log('📄 Loaded migration file')
    console.log(`📊 Migration size: ${migrationSQL.length} characters`)
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
    
    console.log(`🔄 Executing ${statements.length} SQL statements...`)
    
    let successCount = 0
    let errorCount = 0
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i] + ';'
      
      // Skip comments and empty statements
      if (statement.trim().startsWith('--') || statement.trim() === ';') {
        continue
      }
      
      try {
        // Use a simple query execution approach
        const { error } = await supabase.rpc('exec', { sql: statement })
        
        if (error) {
          // Try alternative approach if RPC fails
          if (error.message.includes('Could not find the function')) {
            // For schema and table creation, we can try using the REST API
            console.log(`⚠️  Statement ${i + 1}: Using alternative execution method`)
            // This is a limitation - we'll note it but continue
            errorCount++
          } else {
            console.log(`❌ Statement ${i + 1}: ${error.message}`)
            errorCount++
          }
        } else {
          successCount++
          if (statement.includes('CREATE SCHEMA')) {
            console.log(`  ✅ Created stripe schema`)
          } else if (statement.includes('CREATE TABLE')) {
            const tableName = statement.match(/CREATE TABLE[^(]*stripe\.(\w+)/)?.[1]
            if (tableName) {
              console.log(`  ✅ Created table: ${tableName}`)
            }
          } else if (statement.includes('CREATE INDEX')) {
            const indexName = statement.match(/CREATE INDEX[^(]*(\w+)/)?.[1]
            if (indexName) {
              console.log(`  ✅ Created index: ${indexName}`)
            }
          }
        }
      } catch (err) {
        console.log(`❌ Statement ${i + 1}: ${err instanceof Error ? err.message : 'Unknown error'}`)
        errorCount++
      }
    }
    
    console.log(`\n📊 Migration Summary:`)
    console.log(`   ✅ Successful: ${successCount}`)
    console.log(`   ❌ Errors: ${errorCount}`)
    console.log(`   📁 Total: ${statements.length}`)
    
    // Verify the setup
    console.log('\n🔍 Verifying setup...')
    try {
      const { data, error } = await supabase.rpc('stripe.check_setup')
      
      if (error) {
        console.log('⚠️  Could not verify setup automatically')
        console.log('   Try running: npx tsx scripts/verify-stripe-setup.ts')
      } else if (data) {
        const existingTables = data.filter((row: any) => row.exists).length
        console.log(`✅ Found ${existingTables}/7 expected tables`)
        
        if (existingTables > 0) {
          console.log('\n🎉 Stripe Sync Engine setup completed!')
        }
      }
    } catch (verifyError) {
      console.log('⚠️  Could not verify setup, but migration may have succeeded')
    }
    
    if (successCount > 0) {
      console.log('\n🔗 Next steps:')
      console.log('  1. Verify setup: npx tsx scripts/verify-stripe-setup.ts')
      console.log('  2. Configure Stripe webhooks')
      console.log('  3. Test webhook processing')
      console.log('  4. Monitor the admin dashboard')
      console.log('')
      console.log('📚 Webhook endpoint: /api/stripe/webhook')
      console.log('🔧 Admin dashboard: /admin/stripe')
    } else {
      console.log('\n❌ Migration failed completely')
      console.log('💡 Manual setup required:')
      console.log('   1. Copy the SQL from supabase/migrations/stripe_sync_schema.sql')
      console.log('   2. Run it in your Supabase SQL editor')
      console.log('   3. Verify with: npx tsx scripts/verify-stripe-setup.ts')
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error)
    process.exit(1)
  }
}

runStripeMigration().catch(console.error)
