import dynamic from 'next/dynamic'
import { ComponentType, ReactNode } from 'react'

// Dynamic imports with proper error handling and loading states
export const DynamicThemeProvider = dynamic(
  () => import('@/micro-providers/theme-provider'),
  {
    ssr: false,
    loading: () => null
  }
)

export const DynamicNotificationsProvider = dynamic(
  () => import('@/micro-providers/notifications-provider'),
  {
    ssr: false,
    loading: () => null // Notifications don't need a loading state
  }
)

export const DynamicAuthProvider = dynamic(
  () => import('@/micro-providers/auth-provider'),
  {
    ssr: false,
    loading: () => null
  }
)

export const DynamicSettingsProvider = dynamic(
  () => import('@/micro-providers/settings-provider'),
  {
    ssr: false,
    loading: () => null // Settings don't need a loading state
  }
)

export const DynamicKeyboardProvider = dynamic(
  () => import('@/components/ui/keyboard-shortcuts-provider').then(mod => ({ 
    default: mod.KeyboardShortcutsProvider 
  })),
  {
    ssr: false,
    loading: () => null // Keyboard shortcuts don't need a loading state
  }
)

// Provider registry for dynamic loading
export interface DynamicProviderConfig {
  name: string
  component: ComponentType<{ children: ReactNode }>
  enabled: boolean
  priority: number
  dependencies?: string[]
}

export class DynamicProviderRegistry {
  private static instance: DynamicProviderRegistry
  private providers: Map<string, DynamicProviderConfig> = new Map()

  static getInstance(): DynamicProviderRegistry {
    if (!DynamicProviderRegistry.instance) {
      DynamicProviderRegistry.instance = new DynamicProviderRegistry()
    }
    return DynamicProviderRegistry.instance
  }

  register(config: DynamicProviderConfig): this {
    this.providers.set(config.name, config)
    return this
  }

  getProvider(name: string): DynamicProviderConfig | undefined {
    return this.providers.get(name)
  }

  getEnabledProviders(): DynamicProviderConfig[] {
    return Array.from(this.providers.values())
      .filter(provider => provider.enabled)
      .sort((a, b) => b.priority - a.priority)
  }

  async loadProvider(name: string): Promise<ComponentType<{ children: ReactNode }> | null> {
    const config = this.providers.get(name)
    if (!config || !config.enabled) {
      return null
    }

    try {
      return config.component
    } catch (error) {
      console.error(`Failed to load provider: ${name}`, error)
      return null
    }
  }
}

// Initialize default providers
export function initializeDefaultProviders() {
  const registry = DynamicProviderRegistry.getInstance()

  registry
    .register({
      name: 'theme',
      component: DynamicThemeProvider,
      enabled: true,
      priority: 100
    })
    .register({
      name: 'notifications',
      component: DynamicNotificationsProvider,
      enabled: true,
      priority: 10
    })
    .register({
      name: 'auth',
      component: DynamicAuthProvider,
      enabled: false, // Start disabled
      priority: 80
    })
    .register({
      name: 'settings',
      component: DynamicSettingsProvider,
      enabled: false, // Start disabled
      priority: 70
    })
    .register({
      name: 'keyboard',
      component: DynamicKeyboardProvider,
      enabled: false, // Start disabled
      priority: 20
    })

  return registry
}

// Helper to enable/disable providers at runtime
export function configureProvider(name: string, enabled: boolean) {
  const registry = DynamicProviderRegistry.getInstance()
  const provider = registry.getProvider(name)
  
  if (provider) {
    provider.enabled = enabled
  }
}
