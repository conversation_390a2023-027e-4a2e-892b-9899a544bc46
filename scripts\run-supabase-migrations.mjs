#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// Supabase configuration
const SUPABASE_URL = 'https://xvqeiwrpbzpiqvwuvtpj.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc2MDAyNiwiZXhwIjoyMDY1MzM2MDI2fQ.X68h4b6kdZaLzKoBy7DV8XBmEpRIexErTrBIS-khjko';

// Create Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Migration files in execution order
const migrationFiles = [
  // Core schema first
  'supabase/migrations/001_enhanced_schema.sql',
  
  // Profile enhancements
  'supabase/migrations/002_profile_enhancements.sql',
  
  // Content embeddings (requires vector extension)
  'supabase/migrations/002_content_embeddings.sql',
  
  // Series management
  'supabase/migrations/003_series_management.sql',
  
  // Missing tables and functions
  'supabase/migrations/004_missing_tables.sql',
  
  // Writing goals
  'supabase/migrations/005_writing_goals.sql',
  
  // Collaboration features
  'supabase/migrations/20240108_collaboration_tables.sql',
  
  // Version history
  'supabase/version_history.sql'
];

async function runMigration(filePath, fileName) {
  try {
    console.log(`\n🔄 Running migration: ${fileName}`);
    
    const fullPath = join(projectRoot, filePath);
    const sql = readFileSync(fullPath, 'utf8');
    
    // Split SQL into individual statements
    const statements = sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`   📝 Found ${statements.length} SQL statements`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.length === 0) continue;
      
      try {
        const { error } = await supabase.rpc('exec_sql', { 
          sql_query: statement + ';' 
        });
        
        if (error) {
          // Try direct query execution as fallback
          const { error: directError } = await supabase
            .from('_temp_migration')
            .select('*')
            .limit(0);
          
          // Execute using raw SQL
          const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/exec_sql`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
              'apikey': SUPABASE_SERVICE_ROLE_KEY
            },
            body: JSON.stringify({ sql_query: statement + ';' })
          });
          
          if (!response.ok) {
            console.warn(`   ⚠️  Statement ${i + 1} warning: ${error?.message || 'Unknown error'}`);
          }
        }
      } catch (err) {
        console.warn(`   ⚠️  Statement ${i + 1} error: ${err.message}`);
      }
    }
    
    console.log(`   ✅ Migration completed: ${fileName}`);
    return true;
    
  } catch (error) {
    console.error(`   ❌ Migration failed: ${fileName}`);
    console.error(`   Error: ${error.message}`);
    return false;
  }
}

async function runAllMigrations() {
  console.log('🚀 Starting Supabase migrations...\n');
  
  let successCount = 0;
  let failCount = 0;
  
  for (const filePath of migrationFiles) {
    const fileName = filePath.split('/').pop();
    const success = await runMigration(filePath, fileName);
    
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
    
    // Small delay between migrations
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📊 Migration Summary:');
  console.log(`   ✅ Successful: ${successCount}`);
  console.log(`   ❌ Failed: ${failCount}`);
  console.log(`   📁 Total: ${migrationFiles.length}`);
  
  if (failCount === 0) {
    console.log('\n🎉 All migrations completed successfully!');
  } else {
    console.log('\n⚠️  Some migrations had issues. Check the logs above.');
  }
}

// Run migrations
runAllMigrations().catch(console.error);
