/**
 * Stripe Sync Service
 * Utilities for querying synced Stripe data from the database
 */

import { createClient } from '@supabase/supabase-js'
import { config } from '@/lib/config'

// Create a Supabase client with service role for database access
const supabase = createClient(
  config.supabase.url,
  config.supabase.serviceRoleKey
)

export interface StripeCustomer {
  id: string
  email: string | null
  name: string | null
  created: number
  metadata: Record<string, any>
  // Add other fields as needed
}

export interface StripeSubscription {
  id: string
  customer: string
  status: string
  current_period_start: number
  current_period_end: number
  cancel_at_period_end: boolean
  metadata: Record<string, any>
  // Add other fields as needed
}

export interface StripeInvoice {
  id: string
  customer: string
  subscription: string | null
  status: string
  amount_paid: number
  amount_due: number
  created: number
  // Add other fields as needed
}

export class StripeSyncService {
  /**
   * Get customer by ID from synced data
   */
  static async getCustomer(customerId: string): Promise<StripeCustomer | null> {
    const { data, error } = await supabase
      .from('stripe_customers')
      .select('*')
      .eq('id', customerId)
      .single()

    if (error) {
      console.error('Error fetching customer:', error)
      return null
    }

    return data
  }

  /**
   * Get customer by email from synced data
   */
  static async getCustomerByEmail(email: string): Promise<StripeCustomer | null> {
    const { data, error } = await supabase
      .from('stripe_customers')
      .select('*')
      .eq('email', email)
      .single()

    if (error) {
      console.error('Error fetching customer by email:', error)
      return null
    }

    return data
  }

  /**
   * Get all subscriptions for a customer
   */
  static async getCustomerSubscriptions(customerId: string): Promise<StripeSubscription[]> {
    const { data, error } = await supabase
      .from('stripe_subscriptions')
      .select('*')
      .eq('customer', customerId)
      .order('created', { ascending: false })

    if (error) {
      console.error('Error fetching customer subscriptions:', error)
      return []
    }

    return data || []
  }

  /**
   * Get active subscription for a customer
   */
  static async getActiveSubscription(customerId: string): Promise<StripeSubscription | null> {
    const { data, error } = await supabase
      .from('stripe_subscriptions')
      .select('*')
      .eq('customer', customerId)
      .in('status', ['active', 'trialing'])
      .order('created', { ascending: false })
      .limit(1)
      .single()

    if (error) {
      console.error('Error fetching active subscription:', error)
      return null
    }

    return data
  }

  /**
   * Get invoices for a customer
   */
  static async getCustomerInvoices(customerId: string, limit = 10): Promise<StripeInvoice[]> {
    const { data, error } = await supabase
      .from('stripe_invoices')
      .select('*')
      .eq('customer', customerId)
      .order('created', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching customer invoices:', error)
      return []
    }

    return data || []
  }

  /**
   * Get subscription by ID
   */
  static async getSubscription(subscriptionId: string): Promise<StripeSubscription | null> {
    const { data, error } = await supabase
      .from('stripe_subscriptions')
      .select('*')
      .eq('id', subscriptionId)
      .single()

    if (error) {
      console.error('Error fetching subscription:', error)
      return null
    }

    return data
  }

  /**
   * Get invoice by ID
   */
  static async getInvoice(invoiceId: string): Promise<StripeInvoice | null> {
    const { data, error } = await supabase
      .from('stripe_invoices')
      .select('*')
      .eq('id', invoiceId)
      .single()

    if (error) {
      console.error('Error fetching invoice:', error)
      return null
    }

    return data
  }

  /**
   * Get recent failed payments
   */
  static async getFailedPayments(limit = 10): Promise<StripeInvoice[]> {
    const { data, error } = await supabase
      .from('stripe_invoices')
      .select('*')
      .eq('status', 'open')
      .gt('amount_due', 0)
      .order('created', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching failed payments:', error)
      return []
    }

    return data || []
  }

  /**
   * Get subscription analytics
   */
  static async getSubscriptionAnalytics() {
    const { data, error } = await supabase
      .rpc('get_subscription_analytics')

    if (error) {
      console.error('Error fetching subscription analytics:', error)
      return null
    }

    return data
  }
}
