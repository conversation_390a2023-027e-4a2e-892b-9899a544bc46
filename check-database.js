const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkTables() {
  console.log('🔍 BOOKSCRIBE DATABASE SCHEMA ANALYSIS');
  console.log('=====================================\n');
  
  // Test each expected table by trying to select from it
  const expectedTables = [
    'profiles', 'projects', 'user_subscriptions', 'usage_tracking', 'usage_events',
    'chapters', 'characters', 'story_arcs', 'story_bible', 'story_bibles',
    'selection_profiles', 'reference_materials', 'editing_sessions', 'agent_logs',
    'chapter_versions', 'content_embeddings', 'series', 'series_books',
    'writing_goals', 'writing_goal_progress', 'writing_sessions', 'notifications',
    'ai_suggestions', 'processing_tasks', 'task_progress',
    'collaboration_sessions', 'collaboration_participants', 'project_collaborators'
  ];
  
  const existingTables = [];
  const missingTables = [];
  
  for (const tableName of expectedTables) {
    try {
      const { data, error } = await supabase.from(tableName).select('*').limit(1);
      if (error) {
        missingTables.push(tableName);
        console.log(`❌ ${tableName} - ${error.message}`);
      } else {
        existingTables.push(tableName);
        console.log(`✅ ${tableName} - EXISTS`);
      }
    } catch (err) {
      missingTables.push(tableName);
      console.log(`❌ ${tableName} - ${err.message}`);
    }
  }
  
  console.log(`\nSUMMARY:`);
  console.log(`========`);
  console.log(`✅ Tables found: ${existingTables.length}`);
  console.log(`❌ Tables missing: ${missingTables.length}`);
  
  if (missingTables.length > 0) {
    console.log(`\nMISSING TABLES:`);
    missingTables.forEach(table => console.log(`- ${table}`));
  }
  
  // Check profiles table structure if it exists
  if (existingTables.includes('profiles')) {
    console.log(`\n🔍 CHECKING PROFILES TABLE STRUCTURE:`);
    try {
      const { data, error } = await supabase.from('profiles').select('*').limit(1);
      if (data && data.length > 0) {
        console.log('Profiles table columns:', Object.keys(data[0]));
      } else {
        console.log('Profiles table is empty');
      }
    } catch (err) {
      console.log('Error checking profiles structure:', err.message);
    }
  }
}

checkTables().catch(console.error);