"use client"

import * as React from "react"
import { ThemeProvider as NextThemesProvider } from "next-themes"
import { getAllThemes, DEFAULT_LIGHT_THEME, DEFAULT_DARK_THEME } from "@/lib/themes/theme-registry"

type ThemeProviderProps = {
  children: React.ReactNode
  attribute?: 'class' | 'data-theme' | 'data-mode'
  defaultTheme?: string
  enableSystem?: boolean
  disableTransitionOnChange?: boolean
  storageKey?: string
  themes?: string[]
  value?: { [key: string]: string }
  nonce?: string
  enableColorScheme?: boolean
}

export function ThemeProvider({
  children,
  attribute = "class",
  defaultTheme = "system",
  enableSystem = true,
  disableTransitionOnChange = false,
  storageKey = "bookscribe-theme",
  ...props
}: ThemeProviderProps) {
  const allThemes = getAllThemes()
  const themeValues = allThemes.map(theme => theme.id)

  return (
    <NextThemesProvider
      attribute={attribute}
      defaultTheme={defaultTheme}
      enableSystem={enableSystem}
      disableTransitionOnChange={disableTransitionOnChange}
      storageKey={storageKey}
      themes={[...themeValues, 'system']}
      value={{
        light: DEFAULT_LIGHT_THEME,
        dark: DEFAULT_DARK_THEME,
        system: 'system',
        ...themeValues.reduce((acc, themeId) => {
          acc[themeId] = themeId
          return acc
        }, {} as Record<string, string>)
      }}
      {...props}
    >
      {children}
    </NextThemesProvider>
  )
}