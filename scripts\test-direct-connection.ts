#!/usr/bin/env tsx

/**
 * Test direct database connection to Supabase
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

async function testDirectConnection() {
  console.log('🔍 Testing direct Supabase connection...')
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY
  
  if (!supabaseUrl || !serviceRoleKey) {
    console.error('❌ Missing required environment variables')
    process.exit(1)
  }
  
  console.log(`📍 URL: ${supabaseUrl}`)
  console.log(`🔑 Service Role Key: ${serviceRoleKey.substring(0, 20)}...`)
  
  const supabase = createClient(supabaseUrl, serviceRoleKey)
  
  try {
    // Test 1: Basic connection
    console.log('\n1️⃣ Testing basic connection...')
    const { data: version, error: versionError } = await supabase
      .rpc('version')
    
    if (versionError) {
      console.log('❌ Version check failed:', versionError.message)
    } else {
      console.log('✅ Database version:', version)
    }
    
    // Test 2: Schema check
    console.log('\n2️⃣ Testing schema access...')
    const { data: schemas, error: schemaError } = await supabase
      .from('information_schema.schemata')
      .select('schema_name')
      .limit(5)
    
    if (schemaError) {
      console.log('❌ Schema check failed:', schemaError.message)
    } else {
      console.log('✅ Available schemas:', schemas?.map(s => s.schema_name))
    }
    
    // Test 3: Create schema test
    console.log('\n3️⃣ Testing schema creation...')
    const { data: createResult, error: createError } = await supabase
      .rpc('exec_sql', { sql: 'CREATE SCHEMA IF NOT EXISTS test_stripe;' })
    
    if (createError) {
      console.log('❌ Schema creation failed:', createError.message)
      
      // Try alternative method
      console.log('🔄 Trying alternative schema creation...')
      const { error: altError } = await supabase
        .from('pg_namespace')
        .select('nspname')
        .eq('nspname', 'test_stripe')
        .single()
      
      if (altError && altError.code === 'PGRST116') {
        console.log('✅ Schema does not exist (expected)')
      } else {
        console.log('⚠️  Alternative method result:', altError?.message || 'Success')
      }
    } else {
      console.log('✅ Schema creation successful')
    }
    
    // Test 4: Table creation test
    console.log('\n4️⃣ Testing table creation...')
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS test_stripe.test_table (
        id TEXT PRIMARY KEY,
        name TEXT,
        created_at TIMESTAMP DEFAULT NOW()
      );
    `
    
    const { error: tableError } = await supabase
      .rpc('exec_sql', { sql: createTableSQL })
    
    if (tableError) {
      console.log('❌ Table creation failed:', tableError.message)
    } else {
      console.log('✅ Table creation successful')
    }
    
    // Test 5: Check if we can access existing tables
    console.log('\n5️⃣ Testing existing table access...')
    const { data: tables, error: tableListError } = await supabase
      .from('information_schema.tables')
      .select('table_name, table_schema')
      .eq('table_schema', 'public')
      .limit(5)
    
    if (tableListError) {
      console.log('❌ Table list failed:', tableListError.message)
    } else {
      console.log('✅ Found tables:', tables?.map(t => t.table_name))
    }
    
    // Test 6: Check available RPC functions
    console.log('\n6️⃣ Checking available RPC functions...')
    const { data: functions, error: funcError } = await supabase
      .from('information_schema.routines')
      .select('routine_name')
      .eq('routine_schema', 'public')
      .limit(10)
    
    if (funcError) {
      console.log('❌ Function check failed:', funcError.message)
    } else {
      console.log('✅ Available functions:', functions?.map(f => f.routine_name))
    }
    
    console.log('\n📊 Connection Test Summary:')
    console.log('✅ Supabase client can connect')
    console.log('✅ Service role key is valid')
    console.log('⚠️  Direct SQL execution may be limited')
    console.log('')
    console.log('💡 Recommendation: Use Supabase SQL Editor for schema creation')
    
  } catch (error) {
    console.error('❌ Connection test failed:', error)
  }
}

testDirectConnection().catch(console.error)
