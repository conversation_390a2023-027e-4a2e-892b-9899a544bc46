# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
# Webhook secret (for verifying <PERSON>pa<PERSON> webhooks)
SUPABASE_WEBHOOK_SECRET=your_webhook_secret
SUPABASE_JWT_SECRET=your_jwt_secret

# REQUIRED: Set your OpenAI API key for AI-powered features
OPENAI_API_KEY=your_openai_api_key

# Google Gemini Configuration (optional - for alternative AI model)
GENKIT_API_KEY=your_genkit_api_key

# Stripe Configuration (Required for payments)
# -----------------------------------------------------
# Get these from https://dashboard.stripe.com/apikeys
STRIPE_SECRET_KEY=your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Price IDs from your Stripe dashboard
STRIPE_PRICE_ID_BASIC=your_basic_price_id
STRIPE_PRICE_ID_PRO=your_pro_price_id
STRIPE_PRICE_ID_ENTERPRISE=your_enterprise_price_id

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Development Configuration (optional)
NEXT_PUBLIC_DEV_BYPASS_AUTH=false
DEV_USER_EMAIL=<EMAIL>
DEV_USER_ID=dev-user-123

# Admin Configuration
# Comma-separated list of admin email addresses
ADMIN_EMAILS=<EMAIL>,<EMAIL>

# MCP Server Configuration
# -----------------------------------------------------
# Sentry Configuration (for error monitoring MCP server)
SENTRY_ORG=your_sentry_organization_slug
SENTRY_PROJECT=bookscribe
SENTRY_AUTH_TOKEN=your_sentry_auth_token

# Supabase Personal Access Token (for Supabase MCP server)
# Get this from https://supabase.com/dashboard/account/tokens
SUPABASE_ACCESS_TOKEN=your_supabase_personal_access_token

# Playwright Configuration (optional - for browser automation)
PLAYWRIGHT_BROWSER=chromium
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_TIMEOUT=30000
PLAYWRIGHT_VIEWPORT=1280x720

# Context7 Configuration (optional - for documentation access)
CONTEXT7_MAX_TOKENS=10000