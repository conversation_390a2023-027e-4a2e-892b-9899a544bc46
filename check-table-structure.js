const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkTableStructures() {
  console.log('🔍 DETAILED TABLE STRUCTURE ANALYSIS');
  console.log('====================================\n');
  
  const existingTables = [
    'projects', 'chapters', 'characters', 'story_arcs', 'story_bible',
    'selection_profiles', 'reference_materials', 'editing_sessions', 'agent_logs'
  ];
  
  for (const tableName of existingTables) {
    try {
      console.log(`📋 TABLE: ${tableName.toUpperCase()}`);
      console.log('─'.repeat(40));
      
      const { data, error } = await supabase.from(tableName).select('*').limit(1);
      
      if (error) {
        console.log(`❌ Error: ${error.message}`);
      } else if (data && data.length > 0) {
        const columns = Object.keys(data[0]);
        console.log(`Columns (${columns.length}):`);
        columns.forEach((col, i) => {
          console.log(`  ${i + 1}. ${col}`);
        });
      } else {
        console.log('Table exists but is empty - cannot determine structure');
      }
      console.log();
    } catch (err) {
      console.log(`❌ ${tableName} - ${err.message}\n`);
    }
  }
  
  // Check specific missing features
  console.log('🔍 CHECKING FOR CRITICAL MISSING FEATURES:');
  console.log('==========================================');
  
  // Check if projects table has enhanced fields
  try {
    const { data } = await supabase.from('projects').select('*').limit(1);
    if (data && data.length > 0) {
      const columns = Object.keys(data[0]);
      const expectedProjectFields = [
        'primary_genre', 'subgenre', 'narrative_voice', 'tense', 'writing_style',
        'structure_type', 'pacing_preference', 'target_word_count', 'current_word_count',
        'target_audience', 'content_rating', 'project_scope', 'series_type'
      ];
      
      const missingProjectFields = expectedProjectFields.filter(field => !columns.includes(field));
      
      console.log('📊 PROJECTS TABLE ANALYSIS:');
      if (missingProjectFields.length === 0) {
        console.log('✅ All expected project wizard fields present');
      } else {
        console.log(`❌ Missing project fields (${missingProjectFields.length}):`);
        missingProjectFields.forEach(field => console.log(`   - ${field}`));
      }
    }
  } catch (err) {
    console.log('❌ Cannot analyze projects table structure');
  }
  
  console.log();
  
  // Check chapters table for enhanced fields
  try {
    const { data } = await supabase.from('chapters').select('*').limit(1);
    if (data && data.length > 0) {
      const columns = Object.keys(data[0]);
      const expectedChapterFields = [
        'scenes_data', 'character_states', 'plot_advancement', 'pov_character'
      ];
      
      const missingChapterFields = expectedChapterFields.filter(field => !columns.includes(field));
      
      console.log('📖 CHAPTERS TABLE ANALYSIS:');
      if (missingChapterFields.length === 0) {
        console.log('✅ All expected chapter enhancement fields present');
      } else {
        console.log(`❌ Missing chapter fields (${missingChapterFields.length}):`);
        missingChapterFields.forEach(field => console.log(`   - ${field}`));
      }
    }
  } catch (err) {
    console.log('❌ Cannot analyze chapters table structure');
  }
  
  console.log();
  
  // Check characters table for enhanced fields
  try {
    const { data } = await supabase.from('characters').select('*').limit(1);
    if (data && data.length > 0) {
      const columns = Object.keys(data[0]);
      const expectedCharacterFields = [
        'character_id', 'voice_data', 'character_arc', 'relationships'
      ];
      
      const missingCharacterFields = expectedCharacterFields.filter(field => !columns.includes(field));
      
      console.log('👥 CHARACTERS TABLE ANALYSIS:');
      if (missingCharacterFields.length === 0) {
        console.log('✅ All expected character enhancement fields present');
      } else {
        console.log(`❌ Missing character fields (${missingCharacterFields.length}):`);
        missingCharacterFields.forEach(field => console.log(`   - ${field}`));
      }
    }
  } catch (err) {
    console.log('❌ Cannot analyze characters table structure');
  }
}

checkTableStructures().catch(console.error);