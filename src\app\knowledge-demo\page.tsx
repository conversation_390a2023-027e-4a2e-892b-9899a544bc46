export default function KnowledgeDemoPage() {
  return (
    <div style={{
      minHeight: '100vh',
      background: 'hsl(45, 50%, 97%)',
      color: 'hsl(25, 30%, 10%)',
      fontFamily: 'system-ui, sans-serif'
    }}>
      {/* Header */}
      <header style={{
        borderBottom: '1px solid hsl(40, 25%, 85%)',
        background: 'hsl(42, 45%, 95%)',
        padding: '1rem 2rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <a href="/" style={{
            textDecoration: 'none',
            color: 'inherit',
            padding: '0.5rem 1rem',
            borderRadius: '0.375rem',
            border: '1px solid hsl(40, 25%, 85%)'
          }}>
            ← Back to Home
          </a>

          <h1 style={{ fontSize: '1.25rem', fontWeight: '600', margin: 0 }}>
            Knowledge Panel Demo
          </h1>

          <span style={{
            padding: '0.25rem 0.5rem',
            fontSize: '0.75rem',
            border: '1px solid hsl(40, 25%, 85%)',
            borderRadius: '0.375rem',
            background: 'transparent'
          }}>
            Interactive Demo
          </span>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <button style={{
            padding: '0.5rem 1rem',
            borderRadius: '0.375rem',
            border: '1px solid hsl(40, 25%, 85%)',
            background: 'hsl(25, 75%, 45%)',
            color: 'hsl(45, 50%, 97%)',
            cursor: 'pointer'
          }}>
            💾 Save Demo
          </button>
        </div>
      </header>

      {/* Main Content */}
      <div style={{ display: 'flex', height: 'calc(100vh - 80px)' }}>
        {/* Chapter Navigator */}
        <div style={{
          width: '16rem',
          borderRight: '1px solid hsl(40, 25%, 85%)',
          background: 'hsl(42, 45%, 95%)',
          display: 'flex',
          flexDirection: 'column'
        }}>
          <div style={{
            padding: '1rem',
            borderBottom: '1px solid hsl(40, 25%, 85%)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <h2 style={{ fontSize: '1.125rem', fontWeight: '600', margin: 0 }}>Chapters</h2>
            <button style={{
              padding: '0.25rem 0.5rem',
              borderRadius: '0.375rem',
              border: '1px solid hsl(40, 25%, 85%)',
              background: 'transparent',
              cursor: 'pointer'
            }}>+</button>
          </div>

          <div style={{ flex: 1, overflow: 'auto', padding: '0.5rem' }}>
            {[
              { id: 1, title: 'The Awakening', status: '✅', words: 2500 },
              { id: 2, title: 'Shadows and Whispers', status: '✏️', words: 1200 },
              { id: 3, title: 'The Ancient Oak', status: '⭕', words: 0 }
            ].map((chapter) => (
              <div key={chapter.id} style={{
                padding: '0.75rem',
                marginBottom: '0.5rem',
                borderRadius: '0.5rem',
                background: chapter.id === 1 ? 'hsl(25, 75%, 45%, 0.1)' : 'transparent',
                border: chapter.id === 1 ? '1px solid hsl(25, 75%, 45%, 0.2)' : 'none',
                cursor: 'pointer'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <span>{chapter.status}</span>
                  <span style={{ fontWeight: '500' }}>Chapter {chapter.id}</span>
                </div>
                <div style={{ fontSize: '0.875rem', color: 'hsl(25, 20%, 35%)', marginTop: '0.25rem' }}>
                  {chapter.title}
                </div>
                <div style={{ fontSize: '0.75rem', color: 'hsl(25, 20%, 35%)', marginTop: '0.25rem' }}>
                  {chapter.words} words
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Editor Area */}
        <div style={{ flex: 1, display: 'flex' }}>
          <div style={{ flex: 1, padding: '1rem' }}>
            <div style={{
              height: '100%',
              background: 'hsl(42, 45%, 95%)',
              border: '1px solid hsl(40, 25%, 85%)',
              borderRadius: '0.5rem',
              padding: '1rem'
            }}>
              <div style={{ fontSize: '0.875rem', color: 'hsl(25, 20%, 35%)', marginBottom: '1rem' }}>
                Chapter 1: The Awakening
              </div>
              <div style={{
                fontFamily: 'monospace',
                fontSize: '0.875rem',
                lineHeight: '1.6',
                whiteSpace: 'pre-wrap'
              }}>
                {`The old oak tree stood sentinel at the edge of Willowbrook, its gnarled branches reaching toward the storm-darkened sky. Sarah approached cautiously, her heart pounding as she remembered her grandmother's warnings about the ancient tree and its mysterious powers.

"Never go near the oak when the moon is full," her grandmother had whispered on her deathbed. "The spirits that dwell within are restless, and they hunger for the living."

But Sarah was no longer the frightened child who had cowered beneath her grandmother's tales. She was a scholar now, armed with knowledge and determination...`}
              </div>
            </div>
          </div>

          {/* Right Panel */}
          <div style={{
            width: '24rem',
            borderLeft: '1px solid hsl(40, 25%, 85%)',
            background: 'hsl(42, 45%, 95%)',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <div style={{
              padding: '1rem',
              borderBottom: '1px solid hsl(40, 25%, 85%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <h2 style={{ fontSize: '1.125rem', fontWeight: '600', margin: 0 }}>Project Tools</h2>
              <button style={{
                padding: '0.25rem 0.5rem',
                borderRadius: '0.375rem',
                border: '1px solid hsl(40, 25%, 85%)',
                background: 'transparent',
                cursor: 'pointer'
              }}>✕</button>
            </div>

            <div style={{ flex: 1, padding: '1rem' }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                <div style={{
                  padding: '1rem',
                  background: 'hsl(40, 30%, 90%)',
                  borderRadius: '0.5rem'
                }}>
                  <h3 style={{ fontWeight: '600', margin: '0 0 0.5rem 0' }}>🧠 Knowledge Base</h3>
                  <p style={{ fontSize: '0.875rem', color: 'hsl(25, 20%, 35%)', margin: 0 }}>
                    Select text in the editor to capture it here.
                  </p>
                </div>

                <div style={{
                  padding: '1rem',
                  background: 'hsl(40, 30%, 90%)',
                  borderRadius: '0.5rem'
                }}>
                  <h3 style={{ fontWeight: '600', margin: '0 0 0.5rem 0' }}>🤖 AI Assistant</h3>
                  <p style={{ fontSize: '0.875rem', color: 'hsl(25, 20%, 35%)', margin: 0 }}>
                    AI chat and suggestions would appear here.
                  </p>
                </div>

                <div style={{
                  padding: '1rem',
                  background: 'hsl(40, 30%, 90%)',
                  borderRadius: '0.5rem'
                }}>
                  <h3 style={{ fontWeight: '600', margin: '0 0 0.5rem 0' }}>📖 Story Bible</h3>
                  <p style={{ fontSize: '0.875rem', color: 'hsl(25, 20%, 35%)', margin: 0 }}>
                    Characters, locations, and plot elements.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div style={{
        position: 'fixed',
        bottom: '1.5rem',
        left: '1.5rem',
        maxWidth: '28rem',
        zIndex: 50
      }}>
        <div style={{
          background: 'hsl(42, 45%, 95%, 0.95)',
          border: '1px solid hsl(40, 25%, 85%)',
          borderRadius: '0.5rem',
          padding: '1rem',
          backdropFilter: 'blur(8px)',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
        }}>
          <h3 style={{ fontWeight: '600', margin: '0 0 0.5rem 0' }}>
            📚 Knowledge Panel Demo Instructions:
          </h3>
          <ul style={{
            fontSize: '0.875rem',
            color: 'hsl(25, 20%, 35%)',
            margin: 0,
            paddingLeft: '1rem',
            lineHeight: '1.5'
          }}>
            <li>Select text in the editor to capture it in the Knowledge panel</li>
            <li>Use the right panel to access Knowledge, AI Chat, and Story Bible</li>
            <li>This demo showcases the Writer's Sanctuary theme styling</li>
            <li>All interactions are simulated for demonstration purposes</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

// Demo data
const demoChapters = [
  { id: 'ch1', number: 1, title: 'The Awakening', status: 'completed', wordCount: 2500 },
  { id: 'ch2', number: 2, title: 'Shadows and Whispers', status: 'writing', wordCount: 1200 },
  { id: 'ch3', number: 3, title: 'The Ancient Oak', status: 'planned', wordCount: 0 },
  { id: 'ch4', number: 4, title: 'Secrets Unveiled', status: 'planned', wordCount: 0 },
  { id: 'ch5', number: 5, title: 'The Prophecy', status: 'planned', wordCount: 0 }
]

const sampleContent = `Chapter 1: The Awakening

The old oak tree stood sentinel at the edge of Willowbrook, its gnarled branches reaching toward the storm-darkened sky. Sarah approached cautiously, her heart pounding as she remembered her grandmother's warnings about the ancient tree and its mysterious powers.

"Never go near the oak when the moon is full," her grandmother had whispered on her deathbed. "The spirits that dwell within are restless, and they hunger for the living."

But Sarah was no longer the frightened child who had cowered beneath her grandmother's tales. She was a scholar now, armed with knowledge and determination. The leather-bound journal in her hands contained decades of research about the supernatural phenomena that plagued their small town.

As she drew closer to the massive trunk, the wind began to howl through the branches, creating an otherworldly symphony that seemed to call her name. The bark felt warm beneath her palm, pulsing with an energy that made her fingertips tingle.

Suddenly, the world around her began to shift and blur. The familiar landscape of Willowbrook dissolved into something far more ancient and mysterious. She found herself standing in a moonlit grove where the very air shimmered with magic.

"You have come at last," a voice whispered from the shadows between the trees. Sarah spun around, searching for the source, but saw only dancing shadows and silver light filtering through leaves that seemed to glow with their own inner fire.

The journal in her hands grew warm, its pages fluttering open of their own accord. Words began to appear on the yellowed parchment, written in a script she had never seen before but somehow understood perfectly.

"The time of choosing has arrived. Will you embrace your heritage, or will you turn away as your mother did before you?"

Sarah's breath caught in her throat. Her mother had never spoken of any heritage beyond their simple family history. But as she stared at the mysterious words, memories began to surface—fragments of conversations overheard in childhood, strange dreams that had haunted her sleep, and the inexplicable pull she had always felt toward the old oak.

The wind picked up, swirling around her with increasing intensity. The journal's pages turned rapidly, revealing more secrets with each flutter. Sarah realized she stood at a crossroads, not just in this mystical grove, but in her very existence.

Whatever choice she made in this moment would change everything.`

export default function KnowledgeDemoPage() {
  const [showRightPanel, setShowRightPanel] = useState(true)
  const [showChapterNavigator, setShowChapterNavigator] = useState(true)
  const [currentChapterId, setCurrentChapterId] = useState('ch1')

  // Initialize theme
  useEffect(() => {
    // Apply Writer's Sanctuary theme
    const root = document.documentElement
    root.classList.add('writers-sanctuary-light', 'light')
    root.style.setProperty('--background', '45 50% 97%')
    root.style.setProperty('--foreground', '25 30% 10%')
    root.style.setProperty('--card', '42 45% 95%')
    root.style.setProperty('--card-foreground', '25 30% 10%')
    root.style.setProperty('--border', '40 25% 85%')
    root.style.setProperty('--primary', '25 75% 45%')
    root.style.setProperty('--muted-foreground', '25 20% 35%')
  }, [])
  
  return (
    <div className="h-screen flex flex-col" style={{
      background: 'hsl(45, 50%, 97%)',
      color: 'hsl(25, 30%, 10%)'
    }}>
      {/* Header */}
      <header className="border-b px-4 py-2 flex items-center justify-between" style={{
        background: 'hsl(42, 45%, 95%)',
        borderColor: 'hsl(40, 25%, 85%)'
      }}>
        <div className="flex items-center gap-4">
          <Link href="/" className="px-3 py-1 rounded hover:bg-gray-100">
            ← Back to Home
          </Link>

          <div className="flex items-center gap-2">
            <h1 className="text-lg font-semibold">
              Knowledge Panel Demo
            </h1>
            <span className="px-2 py-1 text-xs border rounded" style={{
              borderColor: 'hsl(40, 25%, 85%)'
            }}>
              Interactive Demo
            </span>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Link href="/customization" className="px-3 py-1 rounded hover:bg-gray-100">
            🎨
          </Link>

          <button
            onClick={() => setShowChapterNavigator(!showChapterNavigator)}
            className="px-3 py-1 rounded hover:bg-gray-100"
            style={{
              background: showChapterNavigator ? 'hsl(25, 75%, 45%, 0.1)' : 'transparent',
              color: showChapterNavigator ? 'hsl(25, 75%, 45%)' : 'inherit'
            }}
          >
            📄
          </button>

          <button
            onClick={() => setShowRightPanel(!showRightPanel)}
            className="px-3 py-1 rounded hover:bg-gray-100"
            style={{
              background: showRightPanel ? 'hsl(25, 75%, 45%, 0.1)' : 'transparent',
              color: showRightPanel ? 'hsl(25, 75%, 45%)' : 'inherit'
            }}
          >
            🧠
          </button>

          <button className="px-3 py-1 rounded" style={{
            background: 'hsl(25, 75%, 45%)',
            color: 'hsl(45, 50%, 97%)'
          }}>
            💾 Save
          </button>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Chapter Navigator */}
        {showChapterNavigator && (
          <div className="w-64 border-r" style={{
            background: 'hsl(42, 45%, 95%)',
            borderColor: 'hsl(40, 25%, 85%)'
          }}>
            <div className="h-full flex flex-col">
              {/* Chapter Navigator Header */}
              <div className="p-4 border-b" style={{ borderColor: 'hsl(40, 25%, 85%)' }}>
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold">Chapters</h2>
                  <button className="px-2 py-1 rounded hover:bg-gray-100">+</button>
                </div>
              </div>

              {/* Chapter List */}
              <div className="flex-1 overflow-y-auto p-2">
                {demoChapters.map((chapter) => (
                  <div
                    key={chapter.id}
                    className="p-3 rounded-lg mb-2 cursor-pointer transition-colors hover:bg-gray-100"
                    style={{
                      background: currentChapterId === chapter.id ? 'hsl(25, 75%, 45%, 0.1)' : 'transparent',
                      border: currentChapterId === chapter.id ? '1px solid hsl(25, 75%, 45%, 0.2)' : 'none'
                    }}
                    onClick={() => setCurrentChapterId(chapter.id)}
                  >
                    <div className="flex items-center gap-2">
                      <span className="text-sm">
                        {chapter.status === 'completed' && '✅'}
                        {chapter.status === 'writing' && '✏️'}
                        {chapter.status === 'planned' && '⭕'}
                      </span>
                      <span className="font-medium">
                        Chapter {chapter.number}
                      </span>
                    </div>
                    <div className="text-sm mt-1" style={{ color: 'hsl(25, 20%, 35%)' }}>
                      {chapter.title}
                    </div>
                    <div className="text-xs mt-1" style={{ color: 'hsl(25, 20%, 35%)' }}>
                      {chapter.wordCount} words
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Main Editor Area */}
        <div className="flex-1 flex">
          {/* Editor */}
          <div className="flex-1">
            <div className="h-full p-4">
              <div className="h-full rounded-lg border p-4" style={{
                background: 'hsl(42, 45%, 95%)',
                borderColor: 'hsl(40, 25%, 85%)'
              }}>
                <div className="text-sm mb-4" style={{ color: 'hsl(25, 20%, 35%)' }}>
                  Loading...
                </div>
                <div className="whitespace-pre-wrap font-mono text-sm">
                  {sampleContent}
                </div>
              </div>
            </div>
          </div>

          {/* Right Panel */}
          {showRightPanel && (
            <div className="w-96 border-l" style={{
              background: 'hsl(42, 45%, 95%)',
              borderColor: 'hsl(40, 25%, 85%)'
            }}>
              <div className="h-full flex flex-col">
                {/* Panel Header */}
                <div className="p-4 border-b" style={{ borderColor: 'hsl(40, 25%, 85%)' }}>
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-semibold">Project Tools</h2>
                    <button
                      className="px-2 py-1 rounded hover:bg-gray-100"
                      onClick={() => setShowRightPanel(false)}
                    >
                      ✕
                    </button>
                  </div>
                </div>

                {/* Panel Content */}
                <div className="flex-1 p-4">
                  <div className="space-y-4">
                    <div className="p-4 rounded-lg" style={{ background: 'hsl(40, 30%, 90%)' }}>
                      <h3 className="font-semibold mb-2">Knowledge Base</h3>
                      <p className="text-sm" style={{ color: 'hsl(25, 20%, 35%)' }}>
                        Select text in the editor to capture it here.
                      </p>
                    </div>

                    <div className="p-4 rounded-lg" style={{ background: 'hsl(40, 30%, 90%)' }}>
                      <h3 className="font-semibold mb-2">AI Assistant</h3>
                      <p className="text-sm" style={{ color: 'hsl(25, 20%, 35%)' }}>
                        AI chat and suggestions would appear here.
                      </p>
                    </div>

                    <div className="p-4 rounded-lg" style={{ background: 'hsl(40, 30%, 90%)' }}>
                      <h3 className="font-semibold mb-2">Story Bible</h3>
                      <p className="text-sm" style={{ color: 'hsl(25, 20%, 35%)' }}>
                        Characters, locations, and plot elements.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Instructions Overlay */}
      <div className="fixed bottom-6 left-6 max-w-md z-50">
        <div className="rounded-lg shadow-lg border p-4" style={{
          background: 'hsl(42, 45%, 95%, 0.95)',
          borderColor: 'hsl(40, 25%, 85%)',
          backdropFilter: 'blur(8px)'
        }}>
          <h3 className="font-semibold mb-2">
            Knowledge Panel Demo Instructions:
          </h3>
          <ul className="text-sm space-y-1" style={{ color: 'hsl(25, 20%, 35%)' }}>
            <li>• Select text in the editor to capture it in the Knowledge panel</li>
            <li>• Use the right panel tabs to switch between Knowledge, AI Chat, Story Bible, and more</li>
            <li>• Toggle panels using the buttons in the header</li>
            <li>• This is a fully interactive demo with the Writer's Sanctuary theme</li>
          </ul>
        </div>
      </div>
    </div>
  )
}