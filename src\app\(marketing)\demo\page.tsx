"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useSimpleTheme } from "@/hooks/use-simple-theme";
import { SettingsButton } from "@/components/settings/settings-modal";
import { 
  PenTool, 
  BookOpen, 
  Brain, 
  BarChart3, 
  Play,
  Star,
  Clock,
  Target,
  Sparkles,
  ArrowRight,
  Feather,
  Sun,
  Moon
} from "lucide-react";
import { DemoWizard } from "@/components/demo/demo-wizard";
import { DemoEditor } from "@/components/demo/demo-editor";
import { DemoAgents } from "@/components/demo/demo-agents";
import { DemoStoryBible } from "@/components/demo/demo-story-bible";
import { DemoAnalytics } from "@/components/demo/demo-analytics";

export default function DemoPage() {
  const [activeDemo, setActiveDemo] = useState<string>("overview");
  const { switchTheme, getCurrentMode } = useSimpleTheme();

  const toggleTheme = () => {
    const currentMode = getCurrentMode();
    if (currentMode === 'light') {
      switchTheme('evening-study-dark');
    } else {
      switchTheme('writers-sanctuary-light');
    }
  };

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Paper texture background */}
      <div className="fixed inset-0 paper-texture opacity-30" />
      
      {/* Header */}
      <header className="relative z-50 border-b border-border bg-background/95 backdrop-blur-xl shadow-sm">
        <div className="container flex h-20 items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary to-primary/80 blur-sm opacity-20" />
              <div className="relative w-10 h-10 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center shadow-md">
                <Feather className="w-6 h-6 text-primary-foreground" />
              </div>
            </div>
            <h1 className="text-2xl font-bold tracking-tight font-literary-display text-foreground">
              BookScribe AI
            </h1>
          </div>
          
          <nav className="flex gap-2 sm:gap-4 items-center">
            <Link href="/customization">
              <Button variant="ghost" className="text-foreground/80 hover:text-foreground hover:bg-accent font-medium">
                Customization
              </Button>
            </Link>
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleTheme}
              className="h-9 w-9"
              aria-label="Toggle theme"
            >
              <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
            </Button>
            <SettingsButton />
            <Link href="/">
              <Button variant="ghost" className="text-foreground/80 hover:text-foreground hover:bg-accent font-medium">
                ← Back to Home
              </Button>
            </Link>
            <Link href="/signup">
              <Button variant="literary" className="font-medium px-6">
                Start Writing Free
                <Feather className="w-4 h-4 ml-1" />
              </Button>
            </Link>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative z-10 py-20 px-4">
        <div className="container max-w-6xl mx-auto text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full border border-primary/30 bg-primary/10 mb-8">
            <Play className="w-4 h-4 text-primary" />
            <span className="text-sm text-primary font-medium">Interactive Demo</span>
          </div>

          <h2 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 font-literary-display">
            <span className="text-foreground">
              Experience BookScribe AI
            </span>
            <br />
            <span className="bg-gradient-to-r from-primary via-primary/80 to-primary/60 bg-clip-text text-transparent">
              In Action
            </span>
          </h2>
          
          <p className="text-base sm:text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed mb-12 font-mono">
            Explore the most advanced AI writing platform for novelists. See how our AI agents, 
            intelligent context engine, and collaborative tools work together to help you write your masterpiece.
          </p>

          {/* Demo Navigation */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 mb-12">
            {[
              { id: "wizard", title: "Project Setup", icon: Sparkles, desc: "AI-guided project creation" },
              { id: "editor", title: "Writing Interface", icon: PenTool, desc: "Smart editor with AI assistance" },
              { id: "agents", title: "AI Agents", icon: Brain, desc: "Multi-agent writing pipeline" },
              { id: "story-bible", title: "Story Bible", icon: BookOpen, desc: "Character & world management" },
              { id: "analytics", title: "Analytics", icon: BarChart3, desc: "Progress tracking & insights" }
            ].map((demo) => (
              <Card 
                key={demo.id}
                className={`cursor-pointer transition-all duration-300 hover:scale-105 border ${
                  activeDemo === demo.id 
                    ? 'border-primary/50 bg-primary/10' 
                    : 'border-border bg-card hover:border-primary/30'
                }`}
                onClick={() => setActiveDemo(demo.id)}
              >
                <CardContent className="p-6 text-center">
                  <demo.icon className={`w-8 h-8 mx-auto mb-3 ${
                    activeDemo === demo.id ? 'text-primary' : 'text-muted-foreground'
                  }`} />
                  <h3 className="font-semibold mb-2 font-literary">{demo.title}</h3>
                  <p className="text-sm text-muted-foreground font-mono">{demo.desc}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Demo Content */}
      <section className="relative z-10 py-12 px-4">
        <div className="container max-w-7xl mx-auto">
          <div className="min-h-[600px]">
            {activeDemo === "overview" && <DemoOverview />}
            {activeDemo === "wizard" && <DemoWizard />}
            {activeDemo === "editor" && <DemoEditor />}
            {activeDemo === "agents" && <DemoAgents />}
            {activeDemo === "story-bible" && <DemoStoryBible />}
            {activeDemo === "analytics" && <DemoAnalytics />}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-20 px-4 border-t border-border">
        <div className="container max-w-4xl mx-auto text-center">
          <h3 className="text-3xl sm:text-4xl font-bold mb-6 font-literary-display">
            Ready to Write Your
            <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent"> Masterpiece?</span>
          </h3>
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto font-literary">
            Join thousands of authors who are already using BookScribe AI to create compelling narratives with unprecedented scale and consistency.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/signup">
              <Button 
                size="lg" 
                variant="literary"
                className="font-medium px-8 py-6 text-lg"
              >
                <Sparkles className="w-5 h-5 mr-2" />
                Start Writing Free
              </Button>
            </Link>
            <Link href="/login">
              <Button 
                size="lg" 
                variant="outline" 
                className="font-medium px-8 py-6 text-lg"
              >
                Sign In
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 border-t border-border py-12 bg-background/95 backdrop-blur-xl">
        <div className="container text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center">
              <Feather className="w-4 h-4 text-primary-foreground" />
            </div>
            <span className="text-lg font-semibold font-literary text-foreground">
              BookScribe AI
            </span>
          </div>
          <p className="text-muted-foreground text-sm mb-4">
            © 2025 BookScribe AI. Empowering authors to write without limits.
          </p>
          <p className="text-xs text-primary">
            This demo showcases simulated features. Sign up to experience the real AI-powered writing platform.
          </p>
        </div>
      </footer>
    </div>
  );
}

// Demo Overview Component
function DemoOverview() {
  return (
    <div className="text-center">
      <h3 className="text-2xl font-bold mb-8 font-literary-display">Choose a demo above to explore BookScribe AI&apos;s features</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-border bg-card">
          <CardContent className="p-6">
            <Target className="w-12 h-12 text-primary mx-auto mb-4" />
            <h4 className="font-semibold mb-2">{"300,000+ Words"}</h4>
            <p className="text-sm text-muted-foreground">Write epic novels with perfect consistency across massive word counts</p>
          </CardContent>
        </Card>
        <Card className="border-border bg-card">
          <CardContent className="p-6">
            <Clock className="w-12 h-12 text-primary mx-auto mb-4" />
            <h4 className="font-semibold mb-2">Real-time AI</h4>
            <p className="text-sm text-muted-foreground">Get instant suggestions and improvements as you write</p>
          </CardContent>
        </Card>
        <Card className="border-border bg-card">
          <CardContent className="p-6">
            <Star className="w-12 h-12 text-primary mx-auto mb-4" />
            <h4 className="font-semibold mb-2">Your Voice</h4>
            <p className="text-sm text-muted-foreground">AI learns and adapts to your unique writing style</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
