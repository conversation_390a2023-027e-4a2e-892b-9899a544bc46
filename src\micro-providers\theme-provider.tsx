'use client'

import { ReactNode, Suspense } from 'react'
import { ThemeProvider as NextThemesProvider } from 'next-themes'
import { ProviderErrorBoundary } from '@/components/error-boundaries/provider-error-boundary'

interface ThemeProviderProps {
  children: ReactNode
  defaultTheme?: string
  enableSystem?: boolean
  storageKey?: string
}

function ThemeProviderCore({ 
  children, 
  defaultTheme = 'light',
  enableSystem = false,
  storageKey = 'bookscribe-theme'
}: ThemeProviderProps) {
  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme={defaultTheme}
      enableSystem={enableSystem}
      disableTransitionOnChange
      storageKey={storageKey}
    >
      {children}
    </NextThemesProvider>
  )
}

export default function ThemeMicroProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <ProviderErrorBoundary 
      providerName="Theme Provider"
      fallback={
        <div className="min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
          {children}
        </div>
      }
    >
      <Suspense fallback={
        <div className="min-h-screen bg-white text-gray-900 flex items-center justify-center">
          <div className="animate-pulse">Loading theme...</div>
        </div>
      }>
        <ThemeProviderCore {...props}>
          {children}
        </ThemeProviderCore>
      </Suspense>
    </ProviderErrorBoundary>
  )
}
