import "./globals.css";
import { ClientProviders } from './client-providers'
import type { Metadata } from 'next'
import { Crimson_Text, JetBrains_Mono } from 'next/font/google'

const crimsonText = Crimson_Text({
  subsets: ['latin'],
  weight: ['400', '600', '700'],
  style: ['normal', 'italic'],
  variable: '--font-crimson-text',
  display: 'swap',
})

const jetbrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600'],
  variable: '--font-jetbrains-mono',
  display: 'swap',
})

export const metadata: Metadata = {
  metadataBase: new URL('http://localhost:3001'),
  title: {
    default: 'BookScribe AI - AI-Powered Novel Writing Platform',
    template: '%s | BookScribe AI'
  },
  description: 'Create epic novels with AI assistance. BookScribe helps you write, edit, and publish books with intelligent story development, character creation, and consistency tracking.',
  keywords: ['AI writing', 'novel writing software', 'book writing tool', 'AI author assistant', 'story generator', 'creative writing AI'],
  authors: [{ name: 'BookScribe AI' }],
  creator: 'BookScribe AI',
  publisher: 'BookScribe AI',
  icons: {
    icon: '/favicon.ico',
    apple: '/apple-touch-icon.png',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://bookscribe.ai',
    siteName: 'BookScribe AI',
    title: 'BookScribe AI - AI-Powered Novel Writing Platform',
    description: 'Create epic novels with AI assistance. Write up to 300,000 words with perfect consistency.',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'BookScribe AI - AI-Powered Novel Writing Platform',
    description: 'Create epic novels with AI assistance. Write up to 300,000 words with perfect consistency.',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
      </head>
      <body className={`${crimsonText.variable} ${jetbrainsMono.variable}`}>
        <a href="#main-content" className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md">
          Skip to main content
        </a>
        <ClientProviders>
          {children}
        </ClientProviders>
      </body>
    </html>
  );
}