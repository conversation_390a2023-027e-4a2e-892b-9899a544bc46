import OpenAI from 'openai';
import type { WorldData, ContinuityData, PlotThread, EstablishedFact as _EstablishedFact } from '@/lib/types/story-bible';

interface Suggestion {
  id: string;
  type: 'grammar' | 'style' | 'character' | 'plot' | 'pacing' | 'dialogue';
  severity: 'error' | 'warning' | 'suggestion';
  message: string;
  range: {
    startLineNumber: number;
    startColumn: number;
    endLineNumber: number;
    endColumn: number;
  };
  replacement?: string;
  explanation?: string;
}

interface StoryBibleData {
  character_data?: CharacterData | null;
  world_data?: WorldData | null;
  continuity_data?: ContinuityData | null;
  [key: string]: unknown;
}

interface ProjectData {
  narrative_voice?: string | null;
  writing_style?: string | null;
  primary_genre?: string | null;
  tone_options?: string[] | null;
  target_audience?: string | null;
  [key: string]: unknown;
}

export class ContentAnalyzer {
  private openai: OpenAI;

  constructor(openai: OpenAI) {
    this.openai = openai;
  }

  async analyzeGrammar(content: string): Promise<Suggestion[]> {
    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        temperature: 0.3,
        messages: [
          {
            role: 'system',
            content: `You are an expert grammar and style checker specifically for creative writing. Analyze the text for:
            1. Grammar errors (subject-verb agreement, tense consistency, etc.)
            2. Punctuation issues (especially dialogue punctuation)
            3. Sentence structure problems
            4. Common writing errors
            
            Return suggestions as JSON array with: id, type, severity, message, range (line/column), replacement (if applicable), explanation.
            Line and column numbers should be 1-indexed.
            Focus only on clear errors, not stylistic preferences.`
          },
          {
            role: 'user',
            content: `Analyze this creative writing text for grammar and punctuation issues:\n\n${content}`
          }
        ],
        response_format: { type: 'json_object' }
      });

      const result = JSON.parse(response.choices[0]?.message?.content || '{"suggestions": []}');
      return this.formatSuggestions(result.suggestions || [], 'grammar');
    } catch (error) {
      console.error('Error in grammar analysis:', error);
      return [];
    }
  }

  async analyzeStyle(content: string, project: ProjectData): Promise<Suggestion[]> {
    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        temperature: 0.4,
        messages: [
          {
            role: 'system',
            content: `You are a creative writing style expert. Analyze text for style consistency based on project settings:
            
            Project Style: ${project.writing_style}
            Genre: ${project.primary_genre}
            Tone: ${Array.isArray(project.tone_options) ? project.tone_options.join(', ') : 'neutral'}
            Target Audience: ${project.target_audience}
            
            Look for:
            1. Style inconsistencies with project settings
            2. Tone mismatches
            3. Inappropriate language for target audience
            4. Show vs tell issues
            5. Repetitive language patterns
            6. Weak verb choices
            7. Adverb overuse
            
            Return JSON with suggestions array.`
          },
          {
            role: 'user',
            content: `Analyze this text for style consistency:\n\n${content}`
          }
        ],
        response_format: { type: 'json_object' }
      });

      const result = JSON.parse(response.choices[0]?.message?.content || '{"suggestions": []}');
      return this.formatSuggestions(result.suggestions || [], 'style');
    } catch (error) {
      console.error('Error in style analysis:', error);
      return [];
    }
  }

  async analyzeNarrative(content: string, project: ProjectData, storyBible: StoryBibleData): Promise<Suggestion[]> {
    try {
      const characters = storyBible?.character_data || {};
      const worldRules = storyBible?.world_data?.rules || [];
      
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        temperature: 0.4,
        messages: [
          {
            role: 'system',
            content: `You are a narrative consistency expert. Analyze text for:
            
            1. Character voice consistency
            2. POV consistency (project uses: ${project.narrative_voice})
            3. Dialogue attribution and tags
            4. Pacing issues
            5. Scene transitions
            6. Sensory details balance
            7. Character behavior consistency
            
            Known Characters: ${JSON.stringify(characters)}
            World Rules: ${worldRules.join('; ')}
            
            Return JSON with suggestions for narrative improvements.`
          },
          {
            role: 'user',
            content: `Analyze this narrative text:\n\n${content}`
          }
        ],
        response_format: { type: 'json_object' }
      });

      const result = JSON.parse(response.choices[0]?.message?.content || '{"suggestions": []}');
      return this.formatSuggestions(result.suggestions || [], 'character');
    } catch (error) {
      console.error('Error in narrative analysis:', error);
      return [];
    }
  }

  async analyzePlotConsistency(content: string, storyBible: StoryBibleData, chapterNumber: number): Promise<Suggestion[]> {
    try {
      if (!storyBible) return [];

      const plotThreads = storyBible.continuity_data?.plotThreads || [];
      const establishedFacts = storyBible.continuity_data?.establishedFacts || [];
      
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        temperature: 0.3,
        messages: [
          {
            role: 'system',
            content: `You are a plot consistency expert. Analyze text for:
            
            1. Contradictions with established facts
            2. Plot holes or logical inconsistencies
            3. Timeline issues
            4. Character knowledge inconsistencies
            5. World-building rule violations
            
            Active Plot Threads: ${JSON.stringify(plotThreads.filter((t: PlotThread) => t.status === 'active'))}
            Established Facts: ${JSON.stringify(establishedFacts)}
            Current Chapter: ${chapterNumber}
            
            Return JSON with plot consistency issues and suggestions.`
          },
          {
            role: 'user',
            content: `Check this chapter content for plot consistency:\n\n${content}`
          }
        ],
        response_format: { type: 'json_object' }
      });

      const result = JSON.parse(response.choices[0]?.message?.content || '{"suggestions": []}');
      return this.formatSuggestions(result.suggestions || [], 'plot');
    } catch (error) {
      console.error('Error in plot analysis:', error);
      return [];
    }
  }

  private formatSuggestions(suggestions: Array<Record<string, unknown>>, defaultType: string): Suggestion[] {
    return suggestions
      .map((suggestion, index) => ({
        id: `${defaultType}_${Date.now()}_${index}`,
        type: (suggestion.type || defaultType) as Suggestion['type'],
        severity: (suggestion.severity || 'suggestion') as Suggestion['severity'],
        message: String(suggestion.message || 'Improvement suggestion'),
        range: this.ensureValidRange(suggestion.range as Record<string, unknown>),
        replacement: String(suggestion.replacement || ''),
        explanation: String(suggestion.explanation || ''),
      }))
      .filter(s => s.message && s.range);
  }

  private ensureValidRange(range: Record<string, unknown>): Suggestion['range'] {
    return {
      startLineNumber: Math.max(1, Number(range?.startLineNumber) || 1),
      startColumn: Math.max(1, Number(range?.startColumn) || 1),
      endLineNumber: Math.max(1, Number(range?.endLineNumber || range?.startLineNumber) || 1),
      endColumn: Math.max(1, Number(range?.endColumn || range?.startColumn) || 1),
    };
  }

  async analyzePacing(content: string): Promise<{
    pacingScore: number;
    tensionCurve: number[];
    suggestions: string[];
  }> {
    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        temperature: 0.4,
        messages: [
          {
            role: 'system',
            content: `You are a pacing analysis expert. Analyze the text and return:
            1. Overall pacing score (0-100)
            2. Tension curve as array of values (0-100) for each paragraph
            3. Specific pacing improvement suggestions
            
            Consider:
            - Sentence length variation
            - Action vs contemplation balance
            - Dialogue vs narrative balance
            - Tension escalation and release
            - Scene transitions
            
            Return as JSON with pacingScore, tensionCurve array, and suggestions array.`
          },
          {
            role: 'user',
            content: `Analyze pacing for this text:\n\n${content}`
          }
        ],
        response_format: { type: 'json_object' }
      });

      const result = JSON.parse(response.choices[0]?.message?.content || '{"pacingScore": 50, "tensionCurve": [], "suggestions": []}');
      return result;
    } catch (error) {
      console.error('Error in pacing analysis:', error);
      return { pacingScore: 50, tensionCurve: [], suggestions: [] };
    }
  }

  async analyzeReadability(content: string, targetAudience: string): Promise<{
    readabilityScore: number;
    ageAppropriate: boolean;
    suggestions: string[];
    metrics: {
      averageSentenceLength: number;
      averageWordsPerSentence: number;
      complexWordsPercentage: number;
      gradeLevel: number;
    };
  }> {
    try {
      // Calculate basic metrics
      const sentences = content.split(/[.!?]+/).filter(s => s.trim());
      const words = content.split(/\s+/).filter(w => w.trim());
      const complexWords = words.filter(word => word.length > 6 || /[A-Z].*[A-Z]/.test(word));
      
      const metrics = {
        averageSentenceLength: sentences.length > 0 ? words.length / sentences.length : 0,
        averageWordsPerSentence: sentences.length > 0 ? words.length / sentences.length : 0,
        complexWordsPercentage: words.length > 0 ? (complexWords.length / words.length) * 100 : 0,
        gradeLevel: this.calculateGradeLevel(words.length, sentences.length, complexWords.length),
      };

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        temperature: 0.3,
        messages: [
          {
            role: 'system',
            content: `You are a readability expert. Analyze text readability for target audience: ${targetAudience}
            
            Current metrics:
            - Average sentence length: ${metrics.averageSentenceLength.toFixed(1)} words
            - Complex words: ${metrics.complexWordsPercentage.toFixed(1)}%
            - Grade level: ${metrics.gradeLevel.toFixed(1)}
            
            Return JSON with:
            - readabilityScore (0-100)
            - ageAppropriate (boolean)
            - suggestions (array of specific improvements)
            
            Consider vocabulary complexity, sentence structure, concepts, and themes.`
          },
          {
            role: 'user',
            content: `Analyze readability:\n\n${content}`
          }
        ],
        response_format: { type: 'json_object' }
      });

      const result = JSON.parse(response.choices[0]?.message?.content || '{"readabilityScore": 50, "ageAppropriate": true, "suggestions": []}');
      
      return {
        ...result,
        metrics,
      };
    } catch (error) {
      console.error('Error in readability analysis:', error);
      return {
        readabilityScore: 50,
        ageAppropriate: true,
        suggestions: [],
        metrics: {
          averageSentenceLength: 0,
          averageWordsPerSentence: 0,
          complexWordsPercentage: 0,
          gradeLevel: 0,
        },
      };
    }
  }

  private calculateGradeLevel(wordCount: number, sentenceCount: number, complexWordCount: number): number {
    if (sentenceCount === 0 || wordCount === 0) return 0;
    
    // Flesch-Kincaid Grade Level formula
    const avgSentenceLength = wordCount / sentenceCount;
    const avgSyllablesPerWord = complexWordCount / wordCount; // Simplified approximation
    
    return (0.39 * avgSentenceLength) + (11.8 * avgSyllablesPerWord) - 15.59;
  }
}