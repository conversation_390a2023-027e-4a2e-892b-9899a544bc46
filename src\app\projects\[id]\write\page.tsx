'use client'

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useSearchParams } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { useEditorStore } from '@/stores/editor-store'
import { MonacoEditor } from '@/components/editor/monaco-editor'
import { AiChatPanel } from '@/components/editor/ai-chat-panel'
import { EnhancedChapterNavigator } from '@/components/editor/enhanced-chapter-navigator'
import { StoryBiblePanel } from '@/components/editor/story-bible-panel'
import { VersionHistoryPanel } from '@/components/version-history/version-history-panel'
import { ChapterReviewPanel } from '@/components/editor/chapter-review-panel'
import { SaveStatusIndicator, useOnlineStatus } from '@/components/version-history/save-status-indicator'
import { useAutoSave, useUnsavedChanges } from '@/hooks/use-auto-save'
import { useChapterGeneration, UserChanges } from '@/hooks/use-chapter-generation'
import { Button } from '@/components/ui/button'
import { 
  Save, 
  MessageSquare, 
  Book, 
  FileText,
  ArrowLeft,
  History,
  BarChart3,
  Wand2,
  Eye
} from 'lucide-react'
import Link from 'next/link'
import { Database } from '@/lib/db/types'
import { User } from '@supabase/supabase-js'

type Project = Database['public']['Tables']['projects']['Row']
type Chapter = Database['public']['Tables']['chapters']['Row']

interface StoryBible {
  characters: Array<{
    id: string
    name: string
    role: string
    description: string
    traits: string[]
  }>
  worldRules: Record<string, string>
  timeline: Array<{
    event: string
    chapter: number
  }>
  plotThreads: never[]
}

export default function WritePage() {
  const params = useParams()
  const searchParams = useSearchParams()
  const projectId = params.id as string
  const chapterId = searchParams.get('chapter')
  
  const {
    content,
    setContent,
    showAiChat,
    showStoryBible,
    showChapterNavigator,
    showVersionHistory,
    toggleAiChat,
    toggleStoryBible,
    toggleChapterNavigator,
    toggleVersionHistory,
    currentChapter,
    setCurrentChapter,
    updateChapterList
  } = useEditorStore()
  
  const [project, setProject] = useState<Project | null>(null)
  const [currentChapterData, setCurrentChapterData] = useState<Chapter | null>(null)
  const [storyBible, setStoryBible] = useState<StoryBible | null>(null) // eslint-disable-line @typescript-eslint/no-unused-vars
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [showWritingStats, setShowWritingStats] = useState(false)
  const [originalGeneratedContent, setOriginalGeneratedContent] = useState<string>('')
  
  const supabase = createClient()
  const isOnline = useOnlineStatus()
  const { setUnsavedChanges } = useUnsavedChanges()

  // Chapter generation hook
  const chapterGeneration = useChapterGeneration(projectId, currentChapterData?.id || '')

  // Auto-save hook
  const { saveNow } = useAutoSave(
    {
      chapterId: currentChapterData?.id || '',
      content,
      wordCount: content.trim().split(/\s+/).filter(word => word.length > 0).length
    },
    {
      enabled: !!currentChapterData?.id && isOnline,
      delay: 3000,
      onSave: (success) => {
        if (success) {
          setLastSaved(new Date())
          setHasUnsavedChanges(false)
          setUnsavedChanges(false)
        }
      },
      onError: (error) => {
        console.error('Auto-save failed:', error)
      }
    }
  )

  useEffect(() => {
    loadUser()
    loadProject()
    loadChapters()
    loadStoryBible()
  }, [projectId]) // eslint-disable-line react-hooks/exhaustive-deps

  const loadUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setUser(user)
  }

  // Track content changes for unsaved state
  useEffect(() => {
    if (currentChapterData && content !== (currentChapterData.content || '')) {
      setHasUnsavedChanges(true)
      setUnsavedChanges(true)
    }
  }, [content, currentChapterData, setUnsavedChanges])

  useEffect(() => {
    if (chapterId) {
      loadChapter(chapterId)
    } else {
      // Load or create first chapter
      loadFirstChapter()
    }
  }, [chapterId]) // eslint-disable-line react-hooks/exhaustive-deps

  const loadProject = async () => {
    const { data } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single()
    
    if (data) setProject(data)
  }

  const loadChapters = async () => {
    const { data } = await supabase
      .from('chapters')
      .select('*')
      .eq('project_id', projectId)
      .order('chapter_number')
    
    if (data) {
      updateChapterList(data.map(ch => ({
        id: ch.id,
        number: ch.chapter_number,
        title: ch.title || `Chapter ${ch.chapter_number}`,
        status: ch.status,
        wordCount: ch.actual_word_count || 0
      })))
    }
  }

  const loadChapter = async (id: string) => {
    const { data } = await supabase
      .from('chapters')
      .select('*')
      .eq('id', id)
      .single()
    
    if (data) {
      setCurrentChapterData(data)
      setContent(data.content || '')
      setCurrentChapter(data.chapter_number)
    }
  }

  const loadFirstChapter = async () => {
    const { data } = await supabase
      .from('chapters')
      .select('*')
      .eq('project_id', projectId)
      .order('chapter_number')
      .limit(1)
      .single()
    
    if (data) {
      setCurrentChapterData(data)
      setContent(data.content || '')
      setCurrentChapter(data.chapter_number)
    } else {
      // Create first chapter
      createNewChapter()
    }
  }

  const loadStoryBible = async () => {
    // Load characters
    const { data: characters } = await supabase
      .from('characters')
      .select('*')
      .eq('project_id', projectId)

    // Load world rules
    const { data: bibleEntries } = await supabase
      .from('story_bible')
      .select('*')
      .eq('project_id', projectId)

    // Load timeline from story arcs
    const { data: timeline } = await supabase
      .from('story_arcs')
      .select('*')
      .eq('project_id', projectId)
      .order('act_number')

    if (characters || bibleEntries || timeline) {
      setStoryBible({
        characters: characters?.map(char => ({
          id: char.id,
          name: char.name,
          role: char.role,
          description: char.description || '',
          traits: char.personality_traits?.traits || []
        })) || [],
        worldRules: bibleEntries?.reduce((acc, entry) => {
          if (entry.entry_type === 'world_rule') {
            acc[entry.entry_key] = entry.entry_data?.value || ''
          }
          return acc
        }, {} as Record<string, string>) || {},
        timeline: timeline?.map(arc => ({
          event: arc.description,
          chapter: arc.act_number || 1
        })) || [],
        plotThreads: []
      })
    }
  }

  const createNewChapter = async () => {
    const chapterNumber = currentChapter || 1
    const { data } = await supabase
      .from('chapters')
      .insert({
        project_id: projectId,
        chapter_number: chapterNumber,
        title: `Chapter ${chapterNumber}`,
        content: '',
        status: 'writing'
      })
      .select()
      .single()

    if (data) {
      setCurrentChapterData(data)
      setContent('')
      loadChapters()
    }
  }

  const saveChapter = async () => {
    if (!currentChapterData) return
    
    setIsSaving(true)
    try {
      const wordCount = content.trim().split(/\s+/).filter(word => word.length > 0).length
      
      const { error } = await supabase
        .from('chapters')
        .update({
          content,
          actual_word_count: wordCount,
          updated_at: new Date().toISOString()
        })
        .eq('id', currentChapterData.id)

      if (!error) {
        setLastSaved(new Date())
        setHasUnsavedChanges(false)
        setUnsavedChanges(false)
        loadChapters() // Refresh chapter list
      }
    } finally {
      setIsSaving(false)
    }
  }

  const handleChapterSelect = (id: string) => {
    window.history.pushState({}, '', `/projects/${projectId}/write?chapter=${id}`)
    loadChapter(id)
  }

  const handleCreateChapter = async () => {
    const newChapterNumber = Math.max(...useEditorStore.getState().chapters.map(ch => ch.number), 0) + 1
    
    const { data } = await supabase
      .from('chapters')
      .insert({
        project_id: projectId,
        chapter_number: newChapterNumber,
        title: `Chapter ${newChapterNumber}`,
        content: '',
        status: 'writing'
      })
      .select()
      .single()

    if (data) {
      loadChapters()
      handleChapterSelect(data.id)
    }
  }

  const handleGenerateChapter = async () => {
    if (!currentChapterData) return

    const generatedData = await chapterGeneration.generateChapter(currentChapterData.chapter_number)
    if (generatedData) {
      setOriginalGeneratedContent(generatedData.content)
      setContent(generatedData.content)
      
      // Start the review process
      chapterGeneration.startReview(generatedData.content, generatedData.content)
    }
  }

  const handleReviewChanges = () => {
    if (originalGeneratedContent && content !== originalGeneratedContent) {
      chapterGeneration.startReview(originalGeneratedContent, content)
    }
  }

  const handleSubmitChanges = async (changes: UserChanges) => {
    const success = await chapterGeneration.submitChanges(changes)
    if (success) {
      setOriginalGeneratedContent('')
      loadChapters() // Refresh chapter list
    }
    return success
  }

  const handleApproveGeneration = async () => {
    const success = await chapterGeneration.approveGeneration()
    if (success) {
      setOriginalGeneratedContent('')
      loadChapters() // Refresh chapter list
    }
    return success
  }

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header */}
      <header className="border-b px-4 py-2 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href={`/projects/${projectId}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Project
            </Button>
          </Link>
          
          <div className="flex items-center gap-2">
            <h1 className="text-lg font-semibold">
              {project?.title || 'Loading...'}
            </h1>
            {currentChapterData && (
              <span className="text-sm text-muted-foreground">
                • Chapter {currentChapterData.chapter_number}
              </span>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <SaveStatusIndicator 
            isSaving={isSaving}
            lastSaved={lastSaved}
            hasUnsavedChanges={hasUnsavedChanges}
            isOnline={isOnline}
            onManualSave={saveNow}
          />
          
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={toggleChapterNavigator}
          >
            <FileText className="h-4 w-4" />
          </Button>
          
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={toggleStoryBible}
          >
            <Book className="h-4 w-4" />
          </Button>
          
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={toggleAiChat}
          >
            <MessageSquare className="h-4 w-4" />
          </Button>
          
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={toggleVersionHistory}
          >
            <History className="h-4 w-4" />
          </Button>

          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setShowWritingStats(!showWritingStats)}
          >
            <BarChart3 className="h-4 w-4" />
          </Button>

          {/* Chapter Generation Controls */}
          {currentChapterData?.status === 'planned' && (
            <Button 
              onClick={handleGenerateChapter}
              disabled={chapterGeneration.state.isGenerating}
              size="sm"
              variant="outline"
            >
              <Wand2 className="h-4 w-4 mr-2" />
              {chapterGeneration.state.isGenerating ? 'Generating...' : 'Generate Chapter'}
            </Button>
          )}

          {originalGeneratedContent && content !== originalGeneratedContent && (
            <Button 
              onClick={handleReviewChanges}
              size="sm"
              variant="outline"
            >
              <Eye className="h-4 w-4 mr-2" />
              Review Changes
            </Button>
          )}
          
          <Button 
            onClick={saveChapter}
            disabled={isSaving}
            size="sm"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Chapter Navigator */}
        {showChapterNavigator && (
          <div className="border-r">
            <EnhancedChapterNavigator
              projectId={projectId}
              currentChapterId={currentChapterData?.id}
              content={content}
              onChapterSelect={handleChapterSelect}
              onCreateChapter={handleCreateChapter}
            />
          </div>
        )}

        {/* Editor */}
        <div className="flex-1 flex">
          <div className="flex-1">
            <MonacoEditor
              initialContent={content}
              onContentChange={setContent}
              onSave={saveChapter}
              showToolbar={true}
              showStats={showWritingStats}
              showAISuggestions={true}
            />
          </div>

          {/* Side Panels */}
          <div className="flex">
            {showVersionHistory && (
              <div className="w-80 border-l">
                <VersionHistoryPanel
                  chapterId={currentChapterData?.id || ''}
                  projectId={projectId}
                  userId={user?.id || ''}
                />
              </div>
            )}

            {showStoryBible && (
              <div className="w-80 border-l">
                <StoryBiblePanel
                  projectId={projectId}
                />
              </div>
            )}

            {showAiChat && (
              <div className="w-96 border-l">
                <AiChatPanel
                  projectId={projectId}
                  chapterId={currentChapterData?.id}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Chapter Review Panel */}
      <ChapterReviewPanel
        isOpen={chapterGeneration.state.isReviewing}
        onClose={() => chapterGeneration.resetState()}
        originalContent={originalGeneratedContent}
        editedContent={content}
        chapterTitle={currentChapterData?.title || `Chapter ${currentChapterData?.chapter_number}`}
        chapterNumber={currentChapterData?.chapter_number || 1}
        projectId={projectId}
        chapterId={currentChapterData?.id || ''}
        onSubmitChanges={handleSubmitChanges}
        onApproveGeneration={handleApproveGeneration}
      />
    </div>
  )
}