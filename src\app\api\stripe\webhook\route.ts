import { NextRequest, NextResponse } from 'next/server'
import { StripeSync } from '@supabase/stripe-sync-engine'
import { config } from '@/lib/config'

// Initialize StripeSync instance
const stripeSync = new StripeSync({
  databaseUrl: `postgresql://postgres:${config.supabase.serviceRoleKey}@db.${config.supabase.url.split('//')[1]?.split('.')[0]}.supabase.co:5432/postgres`,
  stripeSecretKey: config.stripe.secretKey,
  stripeWebhookSecret: config.stripe.webhookSecret,
  backfillRelatedEntities: false,
  autoExpandLists: true,
  schema: 'stripe',
})

export async function POST(request: NextRequest) {
  try {
    // Get the raw body as buffer
    const body = await request.arrayBuffer()
    const rawBody = new Uint8Array(body)
    
    // Get Stripe signature from headers
    const stripeSignature = request.headers.get('stripe-signature')
    
    if (!stripeSignature) {
      console.error('Missing stripe-signature header')
      return NextResponse.json(
        { error: 'Missing stripe-signature header' },
        { status: 400 }
      )
    }
    
    console.log('📥 Processing Stripe webhook...')
    
    // Process the webhook using StripeSync
    await stripeSync.processWebhook(rawBody, stripeSignature)
    
    console.log('✅ Stripe webhook processed successfully')
    
    return NextResponse.json(
      { received: true },
      { status: 200 }
    )
    
  } catch (error) {
    console.error('❌ Error processing Stripe webhook:', error)
    
    // Return 400 for webhook signature verification errors
    if (error instanceof Error && error.message.includes('signature')) {
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      )
    }
    
    // Return 500 for other errors
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Disable body parsing for raw webhook data
export const runtime = 'nodejs'
