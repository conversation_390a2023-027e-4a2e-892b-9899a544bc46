import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { config } from '@/lib/config'
import Stripe from 'stripe'

// Initialize Stripe client
const stripe = new Stripe(config.stripe.secretKey, {
  apiVersion: '2024-12-18.acacia',
})

// Initialize Supabase client with service role
const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey)

// Helper function to sync Stripe data to Supabase
async function syncStripeData(event: Stripe.Event) {
  const { type, data } = event
  const object = data.object as any

  try {
    switch (type) {
      case 'customer.created':
      case 'customer.updated':
        await supabase
          .from('stripe_customers')
          .upsert({
            id: object.id,
            email: object.email,
            name: object.name,
            created: object.created,
            updated: Math.floor(Date.now() / 1000),
            metadata: object.metadata || {},
            raw_data: object
          })
        break

      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        await supabase
          .from('stripe_subscriptions')
          .upsert({
            id: object.id,
            customer: object.customer,
            status: object.status,
            current_period_start: object.current_period_start,
            current_period_end: object.current_period_end,
            cancel_at_period_end: object.cancel_at_period_end,
            created: object.created,
            updated: Math.floor(Date.now() / 1000),
            metadata: object.metadata || {},
            raw_data: object
          })
        break

      case 'invoice.created':
      case 'invoice.updated':
      case 'invoice.payment_succeeded':
      case 'invoice.payment_failed':
        await supabase
          .from('stripe_invoices')
          .upsert({
            id: object.id,
            customer: object.customer,
            subscription: object.subscription,
            status: object.status,
            amount_paid: object.amount_paid || 0,
            amount_due: object.amount_due || 0,
            created: object.created,
            updated: Math.floor(Date.now() / 1000),
            metadata: object.metadata || {},
            raw_data: object
          })
        break

      case 'payment_intent.succeeded':
      case 'payment_intent.payment_failed':
        await supabase
          .from('stripe_payment_intents')
          .upsert({
            id: object.id,
            customer: object.customer,
            amount: object.amount,
            currency: object.currency,
            status: object.status,
            created: object.created,
            updated: Math.floor(Date.now() / 1000),
            metadata: object.metadata || {},
            raw_data: object
          })
        break

      default:
        console.log(`Unhandled event type: ${type}`)
    }
  } catch (error) {
    console.error(`Error syncing ${type}:`, error)
    throw error
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the raw body as text
    const body = await request.text()

    // Get Stripe signature from headers
    const stripeSignature = request.headers.get('stripe-signature')

    if (!stripeSignature) {
      console.error('Missing stripe-signature header')
      return NextResponse.json(
        { error: 'Missing stripe-signature header' },
        { status: 400 }
      )
    }

    console.log('📥 Processing Stripe webhook...')

    // Verify webhook signature
    let event: Stripe.Event
    try {
      event = stripe.webhooks.constructEvent(
        body,
        stripeSignature,
        config.stripe.webhookSecret
      )
    } catch (err) {
      console.error('Webhook signature verification failed:', err)
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      )
    }

    // Sync the data to Supabase
    await syncStripeData(event)

    console.log(`✅ Stripe webhook processed successfully: ${event.type}`)

    return NextResponse.json(
      { received: true, type: event.type },
      { status: 200 }
    )

  } catch (error) {
    console.error('❌ Error processing Stripe webhook:', error)

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Disable body parsing for raw webhook data
export const runtime = 'nodejs'
