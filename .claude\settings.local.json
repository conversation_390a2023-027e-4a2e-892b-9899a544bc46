{"permissions": {"allow": ["Bash(npx create-next-app:*)", "Bash(npx --yes create-next-app@latest . --typescript --tailwind --app --src-dir --import-alias=\"@/*\" --eslint)", "Bash(npx --yes create-next-app@latest bookscribe-app --typescript --tailwind --app --src-dir --import-alias=\"@/*\" --eslint)", "Bash(npm install:*)", "Bash(npm uninstall:*)", "Bash(ls:*)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npx shadcn@latest add:*)", "Bash(npm run build:*)", "Bash(npx next:*)", "Bash(npx tsc:*)", "Bash(npx eslint:*)", "Bash(npm run typecheck:*)", "<PERSON><PERSON>(true)", "Bash(npm run:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(npm ls:*)", "<PERSON><PERSON>(touch:*)", "Bash(for step in structure-pacing-step character-world-step themes-content-step technical-specs-step review-step)", "Bash(do)", "Bash(done)", "Bash(rg:*)", "Bash(cp:*)", "Bash(/dev/null)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(timeout:*)", "Bash(npm:*)", "Bash(node:*)", "Bash(npx ts-node -e \"\nimport { runSchemaTests } from './src/lib/validation/test-schemas';\nrunSchemaTests();\n\")", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git config:*)", "Bash(npx madge:*)", "Bash(NODE_OPTIONS='--max-old-space-size=8192' timeout 30s npx next build --debug 2 >& 1)", "<PERSON><PERSON>(mv:*)", "Bash(npx depcheck:*)", "Bash(awk:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(echo)", "Bash(for file in character-development-grid.tsx development-dimension-selector.tsx arc-prediction-panel.tsx character-arc-visualizer.tsx)", "Bash(do echo \"- src/components/analysis/$file\")", "Bash(git reset:*)", "<PERSON><PERSON>(chmod:*)", "Bash(/mnt/c/Users/<USER>/BookScribe/fix-type-imports.sh:*)", "WebFetch(domain:github.com)", "WebFetch(domain:raw.githubusercontent.com)", "Bash(NODE_OPTIONS=--max-old-space-size=4096 npx next build)", "WebFetch(domain:supabase.com)", "Bash(NODE_OPTIONS='--max-old-space-size=4096' NEXT_TELEMETRY_DISABLED=1 npx next build --debug 2 >& 1)", "<PERSON><PERSON>(killall:*)", "Bash(for file in src/lib/services/ai-orchestrator.ts src/lib/services/content-generator.ts src/lib/services/semantic-search.ts src/lib/supabase/middleware.ts src/lib/auth/server.ts)", "Bash(do echo \"=== $file ===\")", "Bash(for file in src/app/api/subscription/portal/route.ts src/components/wizard/wizard-wrapper.tsx src/components/editor/editor-wrapper.tsx src/components/dashboard/dashboard-wrapper.tsx src/app/error.tsx)", "Bash(for:*)", "Bash(wc:*)", "<PERSON><PERSON>(python3:*)", "Bash(npx @axe-core/cli:*)", "Bash(PORT=3000 npm run dev 2 >& 1)", "Bash(NEXT_CACHE_DISABLED=1 npm run dev)", "Bash(NEXT_PUBLIC_SUPABASE_URL=https://xvqeiwrpbzpiqvwuvtpj.supabase.co SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc2MDAyNiwiZXhwIjoyMDY1MzM2MDI2fQ.X68h4b6kdZaLzKoBy7DV8XBmEpRIexErTrBIS-khjko node -e \"\nconst { createClient } = require('@supabase/supabase-js');\nconst supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);\n\nasync function run() {\n  console.log('🔄 Adding fields to profiles table...');\n  \n  const queries = [\n    'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS username VARCHAR(50) UNIQUE;',\n    'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS bio TEXT;',\n    'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS location VARCHAR(255);',\n    'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS website VARCHAR(500);',\n    'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS writing_goals JSONB DEFAULT \\\\'{\\\\\"daily_words\\\\\": 1000, \\\\\"weekly_hours\\\\\": 10, \\\\\"genre_focus\\\\\": \\\\\"Fiction\\\\\"}\\\\';',\n    'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT \\\\'{\\\\\"public_profile\\\\\": true, \\\\\"email_notifications\\\\\": true, \\\\\"writing_reminders\\\\\": true, \\\\\"beta_features\\\\\": false}\\\\';',\n    'CREATE INDEX IF NOT EXISTS idx_profiles_username ON profiles(username);'\n  ];\n  \n  for (const [i, query] of queries.entries()) {\n    console.log(\\`Query \\${i+1}/\\${queries.length}...\\`);\n    const { error } = await supabase.rpc('exec_sql', { sql: query });\n    if (error) console.warn('Warning:', error.message);\n    else console.log('✅ Success');\n  }\n  \n  console.log('\\\\n🧪 Testing...');\n  const { data, error } = await supabase.from('profiles').select('username, bio, writing_goals').limit(1);\n  if (error) console.warn('Test warning:', error.message);\n  else console.log('✅ Schema update complete!');\n}\n\nrun();\n\")", "WebFetch(domain:stripe.com)", "WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:mcp.so)", "WebFetch(domain:mcp.so)", "<PERSON><PERSON>(claude mcp:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(npx playwright:*)", "mcp__puppeteer__puppeteer_navigate", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)", "Bash(npx cross-env:*)", "<PERSON><PERSON>(git clone:*)", "mcp__puppeteer__puppeteer_screenshot", "Bash(npx @stripe/mcp:*)", "Bash(npx @supabase/mcp-server-supabase:*)", "Bash(npx @upstash/context7-mcp:*)", "<PERSON><PERSON>(env)", "Bash(export NVM_DIR=\"$HOME/.nvm\")", "Bash([ -s \"$NVM_DIR/nvm.sh\" ])", "Bash(. \"$NVM_DIR/nvm.sh\")", "Bash(nvm:*)", "Bash(npx @playwright/mcp:*)", "Bash(npx @sentry/mcp-server:*)", "<PERSON><PERSON>(sudo npx playwright:*)", "Bash(/mnt/c/Users/<USER>/BookScribe/test-mcp-servers.sh:*)", "<PERSON><PERSON>(jq:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "Bash(source ~/.bashrc)", "Bash(SUPABASE_URL=\"https://xvqeiwrpbzpiqvwuvtpj.supabase.co\" SUPABASE_SERVICE_ROLE_KEY=\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc2MDAyNiwiZXhwIjoyMDY1MzM2MDI2fQ.X68h4b6kdZaLzKoBy7DV8XBmEpRIexErTrBIS-khjko\" node -e \"\nconst { createClient } = require('@supabase/supabase-js');\nconst supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);\n\nasync function listTables() {\n  console.log('🔍 Examining BookScribe Database Schema...\\n');\n  \n  // Get all tables in public schema\n  const { data: tables, error } = await supabase\n    .from('information_schema.tables')\n    .select('table_name')\n    .eq('table_schema', 'public')\n    .eq('table_type', 'BASE TABLE');\n    \n  if (error) {\n    console.error('Error fetching tables:', error);\n    return;\n  }\n  \n  console.log('📋 EXISTING TABLES:');\n  console.log('==================');\n  tables.forEach((table, i) => {\n    console.log(\\`\\${i + 1}. \\${table.table_name}\\`);\n  });\n  \n  console.log(\\`\\nTotal tables found: \\${tables.length}\\`);\n}\n\nlistTables().catch(console.error);\n\")", "Bash(SUPABASE_URL=\"https://xvqeiwrpbzpiqvwuvtpj.supabase.co\" SUPABASE_SERVICE_ROLE_KEY=\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc2MDAyNiwiZXhwIjoyMDY1MzM2MDI2fQ.X68h4b6kdZaLzKoBy7DV8XBmEpRIexErTrBIS-khjko\" node -e \"\nconst { createClient } = require('@supabase/supabase-js');\nconst supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);\n\nasync function examineDatabase() {\n  console.log('🔍 BOOKSCRIBE DATABASE SCHEMA ANALYSIS');\n  console.log('=====================================\\n');\n  \n  try {\n    // Use RPC to execute raw SQL\n    const { data: tables, error } = await supabase.rpc('exec_sql', {\n      sql: \\\"\\\"\\\"\n        SELECT table_name \n        FROM information_schema.tables \n        WHERE table_schema = 'public' \n        AND table_type = 'BASE TABLE'\n        ORDER BY table_name;\n      \\\"\\\"\\\"\n    });\n    \n    if (error) {\n      console.error('Error with RPC:', error);\n      // Try alternative approach\n      const response = await fetch(\\`\\${process.env.SUPABASE_URL}/rest/v1/rpc/exec_sql\\`, {\n        method: 'POST',\n        headers: {\n          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,\n          'Authorization': \\`Bearer \\${process.env.SUPABASE_SERVICE_ROLE_KEY}\\`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          sql: \\\"SELECT tablename FROM pg_tables WHERE schemaname = 'public' ORDER BY tablename;\\\"\n        })\n      });\n      \n      if (response.ok) {\n        const result = await response.json();\n        console.log('Tables from pg_tables:', result);\n      } else {\n        console.error('HTTP error:', response.status, await response.text());\n      }\n      return;\n    }\n    \n    console.log('📋 CURRENT TABLES IN DATABASE:');\n    console.log('==============================');\n    \n    if (tables && tables.length > 0) {\n      tables.forEach((row, i) => {\n        console.log(\\`\\${i + 1}. \\${row.table_name}\\`);\n      });\n    } else {\n      console.log('No tables found or empty result');\n    }\n    \n  } catch (err) {\n    console.error('Exception occurred:', err);\n  }\n}\n\nexamineDatabase();\n\")", "Bash(SUPABASE_URL=\"https://xvqeiwrpbzpiqvwuvtpj.supabase.co\" SUPABASE_SERVICE_ROLE_KEY=\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc2MDAyNiwiZXhwIjoyMDY1MzM2MDI2fQ.X68h4b6kdZaLzKoBy7DV8XBmEpRIexErTrBIS-khjko\" node -e \"\nconst { createClient } = require('@supabase/supabase-js');\nconst supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);\n\nasync function checkTables() {\n  console.log('🔍 BOOKSCRIBE DATABASE SCHEMA ANALYSIS');\n  console.log('=====================================\\n');\n  \n  // Test each expected table by trying to select from it\n  const expectedTables = [\n    'profiles', 'projects', 'user_subscriptions', 'usage_tracking', 'usage_events',\n    'chapters', 'characters', 'story_arcs', 'story_bible', 'story_bibles',\n    'selection_profiles', 'reference_materials', 'editing_sessions', 'agent_logs',\n    'chapter_versions', 'content_embeddings', 'series', 'series_books',\n    'writing_goals', 'writing_goal_progress', 'writing_sessions', 'notifications',\n    'ai_suggestions', 'processing_tasks', 'task_progress',\n    'collaboration_sessions', 'collaboration_participants', 'project_collaborators'\n  ];\n  \n  const existingTables = [];\n  const missingTables = [];\n  \n  for (const tableName of expectedTables) {\n    try {\n      const { data, error } = await supabase.from(tableName).select('*').limit(1);\n      if (error) {\n        missingTables.push(tableName);\n        console.log(\\`❌ \\${tableName} - \\${error.message}\\`);\n      } else {\n        existingTables.push(tableName);\n        console.log(\\`✅ \\${tableName} - EXISTS\\`);\n      }\n    } catch (err) {\n      missingTables.push(tableName);\n      console.log(\\`❌ \\${tableName} - \\${err.message}\\`);\n    }\n  }\n  \n  console.log(\\`\\nSUMMARY:');\n  console.log(\\`========');\n  console.log(\\`✅ Tables found: \\${existingTables.length}\\`);\n  console.log(\\`❌ Tables missing: \\${missingTables.length}\\`);\n  \n  if (missingTables.length > 0) {\n    console.log(\\`\\nMISSING TABLES:\\`);\n    missingTables.forEach(table => console.log(\\`- \\${table}\\`));\n  }\n}\n\ncheckTables().catch(console.error);\n\")", "Bash(SUPABASE_URL=\"https://xvqeiwrpbzpiqvwuvtpj.supabase.co\" SUPABASE_SERVICE_ROLE_KEY=\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc2MDAyNiwiZXhwIjoyMDY1MzM2MDI2fQ.X68h4b6kdZaLzKoBy7DV8XBmEpRIexErTrBIS-khjko\" node check-database.js)", "Bash(SUPABASE_URL=\"https://xvqeiwrpbzpiqvwuvtpj.supabase.co\" SUPABASE_SERVICE_ROLE_KEY=\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc2MDAyNiwiZXhwIjoyMDY1MzM2MDI2fQ.X68h4b6kdZaLzKoBy7DV8XBmEpRIexErTrBIS-khjko\" node check-schema-detailed.js)"], "deny": []}}