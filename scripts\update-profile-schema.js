/**
 * <PERSON><PERSON><PERSON> to update the profiles table with enhanced fields
 * Run this to add the missing profile fields for the ProfileSettingsSection
 */

const { createClient } = require('@supabase/supabase-js');
const { readFileSync } = require('fs');
const { join } = require('path');

// Load environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateProfileSchema() {
  try {
    console.log('🔄 Updating profiles table schema...');
    
    // Read the migration file
    const migrationPath = join(__dirname, '../supabase/migrations/002_profile_enhancements.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');
    
    // Execute the migration
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    });
    
    if (error) {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    }
    
    console.log('✅ Profiles table updated successfully!');
    console.log('\n📋 Added fields:');
    console.log('- username (VARCHAR(50), unique)');
    console.log('- bio (TEXT)');
    console.log('- location (VARCHAR(255))');
    console.log('- website (VARCHAR(500))');
    console.log('- writing_goals (JSONB)');
    console.log('- preferences (JSONB)');
    console.log('\n🔧 Added features:');
    console.log('- Automatic profile creation on user signup');
    console.log('- Username index for fast lookups');
    console.log('- Enhanced RLS policies');
    
    // Test the schema
    console.log('\n🧪 Testing profile functionality...');
    const { data: testData, error: testError } = await supabase
      .from('profiles')
      .select('id, email, full_name, username, bio, writing_goals, preferences')
      .limit(1);
    
    if (testError) {
      console.warn('⚠️ Warning: Could not test profile functionality:', testError.message);
    } else {
      console.log('✅ Profile schema test passed!');
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  }
}

// Alternative: Direct SQL execution if RPC is not available
async function updateProfileSchemaDirectSQL() {
  try {
    console.log('🔄 Updating profiles table schema (direct SQL)...');
    
    const queries = [
      `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS username VARCHAR(50) UNIQUE;`,
      `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS bio TEXT;`,
      `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS location VARCHAR(255);`,
      `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS website VARCHAR(500);`,
      `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS writing_goals JSONB DEFAULT '{"daily_words": 1000, "weekly_hours": 10, "genre_focus": "Fiction"}';`,
      `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT '{"public_profile": true, "email_notifications": true, "writing_reminders": true, "beta_features": false}';`,
      `CREATE INDEX IF NOT EXISTS idx_profiles_username ON profiles(username);`
    ];
    
    for (const query of queries) {
      const { error } = await supabase.rpc('exec_sql', { sql: query });
      if (error) {
        console.error(`❌ Failed to execute: ${query}`);
        console.error('Error:', error);
      }
    }
    
    console.log('✅ Schema update completed!');
    
  } catch (error) {
    console.error('❌ Alternative method failed:', error);
    console.log('\n💡 Manual steps:');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Run the migration file: supabase/migrations/002_profile_enhancements.sql');
  }
}

// Run the update
console.log('🚀 BookScribe AI - Profile Schema Update');
console.log('=====================================\n');

updateProfileSchema().catch(() => {
  console.log('\n🔄 Trying alternative method...');
  updateProfileSchemaDirectSQL();
});