'use client'

import { <PERSON>actNode, Suspense, useMemo } from 'react'
import { ProviderErrorBoundary } from '@/components/error-boundaries/provider-error-boundary'
import { ProviderFactory, defaultProviderConfig, ProviderConfig } from '@/lib/providers/provider-factory'
import {
  DynamicProviderRegistry,
  initializeDefaultProviders,
  DynamicThemeProvider,
  DynamicNotificationsProvider
} from '@/lib/providers/dynamic-provider-loader'

interface ProviderBundleProps {
  children: ReactNode
  config?: Partial<ProviderConfig>
  enableAuth?: boolean
  enableSettings?: boolean
  enableKeyboard?: boolean
}

export function ProviderBundle({ 
  children, 
  config = {},
  enableAuth = false,
  enableSettings = false,
  enableKeyboard = false
}: ProviderBundleProps) {
  
  // Merge configuration
  const finalConfig = useMemo(() => ({
    ...defaultProviderConfig,
    ...config,
    auth: { ...defaultProviderConfig.auth, enabled: enableAuth },
    settings: { ...defaultProviderConfig.settings, enabled: enableSettings },
    keyboard: { ...defaultProviderConfig.keyboard, enabled: enableKeyboard }
  }), [config, enableAuth, enableSettings, enableKeyboard])

  // Initialize provider factory
  const providerFactory = useMemo(() => {
    const factory = ProviderFactory.getInstance()
    factory.configure(finalConfig)

    // Register core providers
    factory.register('theme', {
      component: DynamicThemeProvider,
      name: 'Theme Provider',
      priority: 100
    })

    factory.register('notifications', {
      component: DynamicNotificationsProvider,
      name: 'Notifications Provider',
      priority: 10
    })

    return factory
  }, [finalConfig])

  // Initialize dynamic registry
  useMemo(() => {
    const registry = initializeDefaultProviders()
    
    // Configure based on props
    if (enableAuth) registry.getProvider('auth')!.enabled = true
    if (enableSettings) registry.getProvider('settings')!.enabled = true
    if (enableKeyboard) registry.getProvider('keyboard')!.enabled = true
    
    return registry
  }, [enableAuth, enableSettings, enableKeyboard])

  return (
    <ProviderErrorBoundary 
      providerName="Provider Bundle"
      fallback={
        <div className="min-h-screen bg-background text-foreground">
          <div className="container mx-auto px-4 py-8">
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold text-destructive mb-2">
                Provider System Error
              </h1>
              <p className="text-muted-foreground">
                The application is running with limited functionality.
              </p>
            </div>
            {children}
          </div>
        </div>
      }
    >
      <Suspense fallback={
        <div className="min-h-screen bg-background flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Initializing application...</p>
          </div>
        </div>
      }>
        <CoreProviders config={finalConfig}>
          {children}
        </CoreProviders>
      </Suspense>
    </ProviderErrorBoundary>
  )
}

// Core providers that are always loaded
function CoreProviders({ children, config }: { children: ReactNode, config: ProviderConfig }) {
  return (
    <DynamicThemeProvider
      defaultTheme={config.theme?.defaultTheme}
      enableSystem={config.theme?.enableSystem}
      storageKey={config.theme?.storageKey}
    >
      <DynamicNotificationsProvider
        position={config.notifications?.position}
        duration={config.notifications?.duration}
      >
        {children}
      </DynamicNotificationsProvider>
    </DynamicThemeProvider>
  )
}

// Extended provider bundle with all features
export function ExtendedProviderBundle({ 
  children, 
  ...props 
}: ProviderBundleProps) {
  return (
    <ProviderBundle 
      {...props}
      enableAuth={true}
      enableSettings={true}
      enableKeyboard={true}
    >
      {children}
    </ProviderBundle>
  )
}

// Minimal provider bundle for simple pages
export function MinimalProviderBundle({ children }: { children: ReactNode }) {
  return (
    <ProviderBundle config={{ 
      theme: { enabled: true },
      notifications: { enabled: true },
      auth: { enabled: false },
      settings: { enabled: false },
      keyboard: { enabled: false },
      errorHandling: { enabled: true }
    }}>
      {children}
    </ProviderBundle>
  )
}
