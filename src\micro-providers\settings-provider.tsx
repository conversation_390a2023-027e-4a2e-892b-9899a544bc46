'use client'

import { ReactNode, Suspense } from 'react'
import { ProviderErrorBoundary } from '@/components/error-boundaries/provider-error-boundary'

interface SettingsProviderProps {
  children: ReactNode
  persistToStorage?: boolean
  storageKey?: string
}

function SettingsProviderCore({ 
  children, 
  persistToStorage = true,
  storageKey = 'bookscribe-settings'
}: SettingsProviderProps) {
  // Lazy load the actual settings context to avoid circular dependencies
  const SettingsContext = require('@/components/settings/settings-provider').SettingsProvider
  
  return (
    <SettingsContext persistToStorage={persistToStorage} storageKey={storageKey}>
      {children}
    </SettingsContext>
  )
}

export default function SettingsMicroProvider({ children, ...props }: SettingsProviderProps) {
  return (
    <ProviderErrorBoundary 
      providerName="Settings Provider"
      fallback={children}
    >
      <Suspense fallback={children}>
        <SettingsProviderCore {...props}>
          {children}
        </SettingsProviderCore>
      </Suspense>
    </ProviderErrorBoundary>
  )
}
